# Webflow Setup Guide - Step Navigation

## 📋 Visão Geral

Este guia de<PERSON>ha as configurações necessárias no **Webflow** para preparar o template para o sistema de step navigation. Como você não edita HTML diretamente (apenas exporta do Webflow), todas as mudanças devem ser feitas na plataforma Webflow.

## 🎯 Objetivos

- ✅ Preparar seções para funcionar como steps
- ✅ Adicionar classes e atributos necessários
- ✅ Configurar elementos para validação
- ✅ Otimizar para export e integração JavaScript
- ✅ Manter design atual intacto

## 📐 Estrutura Atual vs. Necessária

### ✅ O Que JÁ Está Correto
```
✓ Seções já têm classes identificadoras:
  - _0-home-section-calc-intro
  - _1-section-calc-money  
  - _2-section-calc-ativos
  - _3-section-patrimonio-alocation

✓ Estrutura de container já existe:
  - .page-wrapper como container principal
  - Seções organizadas sequencialmente
```

### 🔧 O Que Precisa Ser Ajustado

#### 1. **Seção Introdução (_0-home-section-calc-intro)**

**Configurações no Webflow:**
- ✅ **Classe já existe**: `_0-home-section-calc-intro`
- 🔧 **Adicionar atributo customizado**:
  - **Nome**: `data-step`
  - **Valor**: `0`
- 🔧 **Adicionar classe adicional**: `step-section`

**Como fazer no Webflow:**
1. Selecione a seção de introdução
2. No painel de settings (⚙️), vá para "Custom Attributes"
3. Adicione: `data-step` = `0`
4. No painel de classes, adicione a classe `step-section`

#### 2. **Seção Money (_1-section-calc-money)**

**Configurações necessárias:**
- ✅ **Classe principal**: `_1-section-calc-money` 
- 🔧 **Atributo**: `data-step` = `1`
- 🔧 **Classe adicional**: `step-section`
- 🔧 **Input principal**: Verificar se tem atributo `is-main="true"`

**Verificações especiais:**
1. **Input de Renda**: 
   - Deve ter atributo personalizado `is-main="true"`
   - Classe sugerida: `main-input` ou similar
   - Placeholder deve estar configurado

**Como configurar o input principal:**
1. Selecione o campo de input de renda
2. Custom Attributes → `is-main` = `true`
3. Verifique se o input tem ID ou classe identificadora

#### 3. **Seção Assets (_2-section-calc-ativos)**

**Configurações necessárias:**
- ✅ **Classe principal**: `_2-section-calc-ativos`
- 🔧 **Atributo**: `data-step` = `2`  
- 🔧 **Classe adicional**: `step-section`
- 🔧 **Produtos selecionáveis**: Configurar classes de estado

**Elementos de produtos:**
1. **Container de produtos**: Verificar se tem classe `.produto-item`
2. **Estados de seleção**: 
   - Classe para produto selecionado: `.is-selected`
   - Ou atributo: `data-selected="true"`

**Como configurar produtos:**
1. Selecione cada item de produto
2. Verifique se tem classe `produto-item`
3. Configure interaction para adicionar classe `is-selected` quando clicado
4. Configure visual feedback para estado selecionado

#### 4. **Seção Allocation (_3-section-patrimonio-alocation)**

**Configurações necessárias:**
- ✅ **Classe principal**: `_3-section-patrimonio-alocation`
- 🔧 **Atributo**: `data-step` = `3`
- 🔧 **Classe adicional**: `step-section` 
- 🔧 **Elementos de alocação**: Configurar atributos de dados

**Elementos de alocação:**
1. **Itens interativos**: Classe `.patrimonio_interactive_item`
2. **Percentuais**: Atributo `data-allocated-percentage`
3. **Sliders**: Verificar classes range-slider

**Como configurar:**
1. Cada item de alocação deve ter `data-allocated-percentage="0"`
2. Sliders devem atualizar este atributo via JavaScript
3. Verificar se elementos têm IDs únicos

## 🎨 Configurações de Design

### Container Principal

**Page Wrapper:**
- ✅ Classe existe: `.page-wrapper`
- 🔧 **Adicionar classe**: `calc-variation` (se não existir)

### Responsividade

**Breakpoints a verificar:**
- **Mobile**: 479px e abaixo
- **Tablet**: 768px - 991px  
- **Desktop**: 992px e acima

**Verificações necessárias:**
1. Todas as seções são responsivas
2. Inputs funcionam bem em touch devices
3. Elementos interativos têm tamanho adequado para touch

### Espaçamento para Navigation

**Importante**: Reserve espaço para a barra de navegação:

1. **Mobile**: 
   - Adicione `padding-bottom: 120px` na última seção
   - Ou configure viewport height considerando navigation

2. **Desktop**:
   - Navigation ficará no topo, sem padding extra necessário

**Como fazer:**
1. Crie uma classe utilitária: `nav-spacing-mobile`
2. Configure padding-bottom: 120px
3. Aplique apenas em mobile (use breakpoints)
4. Adicione esta classe ao page-wrapper ou última seção

## 🔗 Interactions e Animations

### Produtos Selecionáveis

**Configurar Interaction:**
1. **Trigger**: Click on `.produto-item`
2. **Action**: Add class `.is-selected`
3. **Duration**: 0.3s ease
4. **Visual feedback**: 
   - Background color change
   - Border highlight
   - Scale transform (opcional)

**Exemplo de states:**
```
Normal state: 
- Background: transparent
- Border: 1px solid #e0e0e0

Selected state (.is-selected):
- Background: rgba(0,0,0,0.05)  
- Border: 2px solid #000
- Transform: scale(1.02) [opcional]
```

### Range Sliders

**Verificações necessárias:**
1. **Elemento**: `range-slider` custom element
2. **Atributos**: `min`, `max`, `value`, `step`
3. **Classes**: Para styling personalizado

**Configuração sugerida:**
- `min="0"`
- `max="100"`  
- `step="0.01"`
- `value="0"`

## 📱 Mobile Optimization

### Touch Targets

**Verificar elementos mínimos:**
- Botões: 44px × 44px mínimo
- Inputs: 48px altura mínima
- Links: área touch de 44px mínimo

### Viewport Configuration

**Meta tag necessária** (provavelmente já existe):
```html
<meta name="viewport" content="width=device-width, initial-scale=1">
```

### Form Elements

**Otimizações mobile:**
1. **Input types corretos**:
   - `type="tel"` para campos numéricos
   - `type="email"` se houver email
   - `inputmode="numeric"` para valores monetários

2. **Autocomplete attributes**:
   - `autocomplete="off"` para calculadora
   - Ou valores específicos se aplicável

## ⚙️ Custom Attributes Resumo

### Aplicar em TODAS as seções:

| Seção | Classe Principal | data-step | Classe Extra |
|-------|------------------|-----------|--------------|
| Intro | `_0-home-section-calc-intro` | `0` | `step-section` |
| Money | `_1-section-calc-money` | `1` | `step-section` |
| Assets | `_2-section-calc-ativos` | `2` | `step-section` |
| Allocation | `_3-section-patrimonio-alocation` | `3` | `step-section` |

### Aplicar em elementos específicos:

| Elemento | Atributo | Valor | Finalidade |
|----------|----------|-------|------------|
| Input principal | `is-main` | `true` | Identificação para validação |
| Produtos | classe | `produto-item` | Seleção de produtos |
| Produtos selecionados | classe | `is-selected` | Estado de seleção |
| Itens alocação | `data-allocated-percentage` | `0` | Tracking de percentual |

## 🧪 Testes no Webflow

### Antes de Exportar:

1. **Preview responsivo**:
   - Teste em Mobile, Tablet, Desktop
   - Verifique se interactions funcionam
   - Confirme touch targets adequados

2. **Accessibility check**:
   - Teste navegação por teclado
   - Verifique contraste de cores
   - Confirme alt texts em imagens

3. **Form functionality**:
   - Teste todos os inputs
   - Verifique sliders
   - Confirme seleção de produtos

### Checklist Pré-Export:

- [ ] Todas as seções têm `data-step` e `step-section`
- [ ] Input principal tem `is-main="true"`
- [ ] Produtos têm classe `produto-item`
- [ ] Interactions de seleção funcionam
- [ ] Range sliders estão configurados
- [ ] Responsive funciona em todos breakpoints
- [ ] Espaçamento para navigation está reservado
- [ ] Custom attributes estão corretos

## 📤 Export Process

### Configurações de Export:

1. **Minify CSS**: ✅ Habilitado
2. **Minify JS**: ✅ Habilitado  
3. **Include jQuery**: ✅ Se estiver usando
4. **Export folder**: Substitua a pasta `Modelo - Webflow` existente

### Pós-Export:

1. **Backup**: Sempre faça backup da pasta anterior
2. **Replace**: Substitua completamente a pasta `Modelo - Webflow`
3. **Git commit**: Commit as mudanças como "Webflow export update"
4. **Test**: Teste a integração com step navigation

## 🐛 Troubleshooting

### Problemas Comuns:

**1. Seções não são encontradas:**
- Verifique se classes principais estão corretas
- Confirme que não há espaços extras nas classes
- Teste no console: `document.querySelector('._0-home-section-calc-intro')`

**2. Validação não funciona:**
- Verifique atributo `is-main="true"` no input
- Confirme que input tem valor quando preenchido
- Teste: `document.querySelector('[is-main="true"]').value`

**3. Produtos não selecionam:**
- Verifique classe `produto-item` 
- Confirme interaction adiciona classe `is-selected`
- Teste: `document.querySelectorAll('.produto-item.is-selected')`

**4. Alocação não calcula:**
- Verifique atributos `data-allocated-percentage`
- Confirme que sliders atualizam estes valores
- Teste: `document.querySelectorAll('[data-allocated-percentage]')`

### Debug Commands:

```javascript
// No console do browser após export:

// Verificar seções
document.querySelectorAll('.step-section').length // Deve ser 4

// Verificar input principal  
document.querySelector('[is-main="true"]') // Não deve ser null

// Verificar produtos
document.querySelectorAll('.produto-item').length // Deve corresponder ao número de produtos

// Verificar atributos de step
Array.from(document.querySelectorAll('[data-step]')).map(el => el.getAttribute('data-step'))
// Deve retornar: ["0", "1", "2", "3"]
```

## 📋 Checklist Final

### Pré-implementação:
- [ ] Todas as configurações do Webflow aplicadas
- [ ] Export realizado e testado
- [ ] Backup da versão anterior feito
- [ ] Debug commands confirmam estrutura correta

### Pós-implementação:
- [ ] Step navigation funcionando
- [ ] Validações operacionais  
- [ ] Responsive adequado
- [ ] Performance satisfatória
- [ ] Acessibilidade verificada

---

## 🚀 Próximos Passos

1. **Aplicar configurações** seguindo este guia
2. **Exportar** nova versão do Webflow
3. **Implementar** step navigation JavaScript
4. **Testar** integração completa
5. **Deploy** versão final

Com essas configurações, seu template Webflow estará pronto para receber o sistema de step navigation sem quebrar funcionalidades existentes!