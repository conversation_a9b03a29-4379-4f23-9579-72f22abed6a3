# App Calc Reino - Sistema Modular de Calculadora Financeira

Este projeto extrai e organiza o código JavaScript do modelo Webflow em uma estrutura modular limpa e profissional.

## 📁 Estrutura do Projeto

```
src/
├── app-calc-reino.js          # Sistema principal que coordena todos os módulos
└── modules/
    ├── currency-control.js     # Controle de botões +/- do input principal
    ├── currency-formatting.js  # Formatação e validação de entradas monetárias
    ├── motion-animation.js     # Animações de botões e interações
    ├── product-system.js       # Sistema de produtos/patrimônio com sliders
    ├── openai-allocation.js    # Integração com OpenAI para alocação de ativos
    ├── section-visibility.js   # Controle de visibilidade das seções
    ├── patrimony-sync.js       # Sincronização entre valores principais e individuais
    └── chart-animations.js     # Animações de gráficos com GSAP
```

## 🚀 Uso

### Inicialização Automática

O sistema se inicializa automaticamente quando o DOM estiver pronto:

```javascript
// O sistema detecta automaticamente quando está pronto
// Não é necessário código adicional para inicialização básica
```

### Inicialização Manual

```javascript
import AppCalcReino from './src/app-calc-reino.js';

// Inicialização com configurações customizadas
AppCalcReino.init({
  enableLogging: true,
  enableAnimations: true,
  enableOpenAI: true,
  enableSync: true
});
```

### Acesso aos Módulos

```javascript
// Acessa módulos específicos
const currencyControl = AppCalcReino.getModule('currencyControl');
const patrimonySync = AppCalcReino.getModule('patrimonySync');

// Exemplo: atualizar valor principal
patrimonySync.setMainValue(100000);

// Exemplo: obter valores individuais
const values = patrimonySync.getIndividualValues();
```

## 📦 Módulos Disponíveis

### 1. **Currency Control System**

Gerencia os botões de incremento/decremento do input principal.

**Características:**

- Cálculo inteligente de incrementos baseado no valor atual
- Suporte a valores grandes com incrementos proporcionais
- Integração com Currency.js para formatação

**API:**

```javascript
const control = AppCalcReino.getModule('currencyControl');
control.increaseValue(); // Aumenta valor
control.decreaseValue(); // Diminui valor
```

### 2. **Currency Formatting System**

Sistema avançado de formatação e validação de entradas monetárias.

**Características:**

- Formatação em tempo real para Real Brasileiro (pt-BR)
- Validação de entrada e prevenção de valores inválidos
- Observadores DOM para detectar mudanças externas
- Rastreamento de alocações

**API:**

```javascript
const formatting = AppCalcReino.getModule('currencyFormatting');
formatting.formatValue(50000); // "R$ 50.000,00"
```

### 3. **Motion Animation System**

Animações interativas usando Motion.js.

**Características:**

- Efeitos hover em botões
- Animações de pressão (press)
- Controles de seta animados
- Efeitos ripple em inputs de moeda

**API:**

```javascript
const motion = AppCalcReino.getModule('motionAnimation');
motion.addHoverEffect(element);
motion.addRippleEffect(element);
```

### 4. **Product System**

Sistema complexo para gerenciar items de patrimônio com sliders.

**Características:**

- Estados ativos/inativos com transições suaves
- Sincronização entre inputs e sliders
- Sistema de pinning para manter items ativos
- Controle de arraste em sliders

**API:**

```javascript
const products = AppCalcReino.getModule('productSystem');
// Sistema gerencia automaticamente os itens encontrados no DOM
```

### 5. **OpenAI Allocation System**

Integração com OpenAI para sugestões de alocação de ativos.

**Características:**

- Múltiplos perfis de risco (conservador, moderado, arrojado, etc.)
- Integração com API OpenAI
- Estados de loading e tratamento de erros
- Formatação de resultados

**API:**

```javascript
const openai = AppCalcReino.getModule('openaiAllocation');
openai.selectProfile('moderado');
openai.updateCurrentValue(100000);
openai.generateAllocation();
```

### 6. **Section Visibility System**

Controla a exibição de diferentes seções da aplicação.

**Características:**

- Transições animadas entre seções
- Controle por URL/hash
- Atalhos de teclado
- Estados ativos em navegação

**API:**

```javascript
const sections = AppCalcReino.getModule('sectionVisibility');
sections.showSection('resultado');
sections.getCurrentSection(); // 'calculadora'
```

### 7. **Patrimony Sync System**

Sincroniza valores entre input principal e inputs individuais.

**Características:**

- Distribuição proporcional baseada em porcentagens
- Sincronização bidirecional
- Atualização de sliders automaticamente
- Observadores para mudanças externas

**API:**

```javascript
const sync = AppCalcReino.getModule('patrimonySync');
sync.setMainValue(200000);
sync.getIndividualValues(); // [80000, 60000, 60000]
sync.getTotalValue(); // 200000
```

### 8. **Chart Animation System**

Animações avançadas para gráficos usando GSAP.

**Características:**

- Suporte a gráficos de pizza, barras e linhas
- Animações baseadas em viewport (Intersection Observer)
- Atualizações dinâmicas de dados
- Controles de replay e pausa

**API:**

```javascript
const charts = AppCalcReino.getModule('chartAnimations');
charts.animateChart('pie-0');
charts.updateChartData('bar-0', newData);
charts.replayChart('line-0');
```

## 🔧 Dependências Externas

O sistema aguarda automaticamente as seguintes bibliotecas:

- **Currency.js** (v2.0.4): Cálculos financeiros precisos
- **Motion.js**: Animações suaves e interativas  
- **GSAP** (v3.13.0): Animações de timeline para gráficos

## ⚙️ Configuração

### ESLint

Configurado para ambiente browser com suporte a:

- Globals do navegador (window, document, etc.)
- Bibliotecas externas (Currency, Motion, GSAP)
- Regras customizadas para console

### Package.json

```json
{
  "type": "module",
  "scripts": {
    "dev": "node bin/live-reload.js",
    "build": "node bin/build.js"
  }
}
```

## 🔄 Comunicação entre Módulos

O sistema principal (`app-calc-reino.js`) coordena a comunicação:

```javascript
// Exemplo: Currency Formatting → Patrimony Sync
currencyFormatting.onMainValueChange = (value) => {
  patrimonySync.updateCurrentValue(value);
};

// Exemplo: Section Visibility → Chart Animations
sectionVisibility.onSectionShow = (sectionName) => {
  if (sectionName === 'resultado') {
    chartAnimations.replayChart('pie-0');
  }
};
```

## 📊 Status do Sistema

```javascript
// Verificar status completo
const status = AppCalcReino.getSystemStatus();
console.log(status);
/*
{
  initialized: true,
  modules: {
    currencyControl: { loaded: true, initialized: true },
    patrimonySync: { loaded: true, initialized: true },
    // ... outros módulos
  },
  config: { enableLogging: true, ... }
}
*/
```

## 🛠️ Métodos de Controle

```javascript
// Controlar módulos individualmente
AppCalcReino.enableModule('motionAnimation');
AppCalcReino.disableModule('openaiAllocation');
AppCalcReino.restartModule('patrimonySync');

// Atualizar configurações
AppCalcReino.updateConfig({
  enableAnimations: false,
  enableOpenAI: false
});

// Destruir sistema (para limpeza)
AppCalcReino.destroy();
```

## 🎯 Características Técnicas

- **Modular**: Cada funcionalidade em módulo separado
- **Assíncrono**: Aguarda dependências automaticamente
- **Resiliente**: Continua funcionando mesmo se módulos falharem
- **Observável**: Sistema de eventos entre módulos
- **Configurável**: Opções flexíveis de inicialização
- **Debugável**: Logging detalhado opcional

## 📝 Notas de Implementação

1. **Preservação de Funcionalidade**: Todo código original foi preservado e apenas reorganizado
2. **Compatibilidade**: Mantém total compatibilidade com o modelo Webflow original
3. **Performance**: Inicialização otimizada com carregamento sob demanda
4. **Manutenibilidade**: Estrutura clara facilita futuras modificações
5. **Escalabilidade**: Fácil adição de novos módulos ao sistema

## 🔗 Integração com HTML

Para usar no modelo Webflow, substitua os scripts inline por:

```html
<script type="module" src="./src/app-calc-reino.js"></script>
```

O sistema detectará automaticamente todos os elementos necessários no DOM e inicializará as funcionalidades correspondentes.
