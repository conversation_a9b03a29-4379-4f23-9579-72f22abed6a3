@font-face {
  font-family: <PERSON><PERSON> Variable;
  src: url('../fonts/Satoshi-Variable.woff2') format("woff2");
  font-weight: 300 900;
  font-style: normal;
  font-display: swap;
}

.w-checkbox {
  margin-bottom: 5px;
  padding-left: 20px;
  display: block;
}

.w-checkbox:before {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-checkbox:after {
  content: " ";
  clear: both;
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-checkbox-input {
  float: left;
  margin: 4px 0 0 -20px;
  line-height: normal;
}

.w-checkbox-input--inputType-custom {
  border: 1px solid #ccc;
  border-radius: 2px;
  width: 12px;
  height: 12px;
}

.w-checkbox-input--inputType-custom.w--redirected-checked {
  background-color: #3898ec;
  background-image: url('https://d3e54v103j8qbb.cloudfront.net/static/custom-checkbox-checkmark.589d534424.svg');
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  border-color: #3898ec;
}

.w-checkbox-input--inputType-custom.w--redirected-focus {
  box-shadow: 0 0 3px 1px #3898ec;
}

.w-form-formradioinput--inputType-custom {
  border: 1px solid #ccc;
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.w-form-formradioinput--inputType-custom.w--redirected-focus {
  box-shadow: 0 0 3px 1px #3898ec;
}

.w-form-formradioinput--inputType-custom.w--redirected-checked {
  border-width: 4px;
  border-color: #3898ec;
}

body {
  color: #333;
  font-family: Satoshi Variable, Arial, sans-serif;
  font-size: 1em;
  font-weight: 500;
  line-height: 1.5;
}

._3-section-patrimonio-alocation {
  width: 100%;
  max-height: 100svh;
  display: none;
}

.adicionar_nova_ativo {
  grid-column-gap: 18px;
  grid-row-gap: 18px;
  margin-top: 20px;
  display: flex;
}

.text-size-medium {
  font-size: 1.13rem;
}

.range-porcentagem {
  width: 100%;
}

.money_content_left-wrapper {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  position: relative;
}

.padding-espec {
  padding-left: 32px;
  padding-right: 32px;
}

.money_content-wrapper {
  justify-content: space-between;
  align-items: stretch;
  height: auto;
  padding-top: 2.5em;
  padding-bottom: 0;
  display: flex;
  position: relative;
  top: 10px;
}

.counter_ativos {
  font-size: 18px;
  font-weight: 500;
}

.intro_content-wrapper {
  flex-flow: column;
  justify-content: center;
  align-items: center;
  height: 100svh;
  padding-top: 0;
  display: flex;
}

.arrow {
  position: relative;
}

.arrow.is-2 {
  position: absolute;
  top: 0%;
  bottom: 0%;
  left: -1.44rem;
}

.ativos_main_drop_area {
  color: #000;
  flex-flow: wrap;
  justify-content: flex-start;
  align-items: stretch;
  width: 100%;
  height: 100%;
  margin-top: 32px;
  margin-bottom: 0;
  padding-top: 0;
  display: flex;
}

.next-content {
  grid-column-gap: 14px;
  grid-row-gap: 14px;
  flex-flow: column;
  max-width: 18rem;
  margin-top: auto;
  margin-bottom: 0;
  display: flex;
  position: absolute;
  inset: auto auto 0% 0%;
}

.next-content.v2 {
  max-width: none;
  position: relative;
}

.next-content.vv {
  bottom: 0;
  left: 0;
}

.texto-info {
  opacity: 0;
  font-size: 0;
  transition: all .3s;
}

.texto-info.ativo {
  opacity: 1;
  margin-left: 8px;
  margin-right: 8px;
  font-size: 20px;
  display: none;
}

.ativos_counter-wrapper {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.heading-style-h2-em {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 4.38em;
}

.heading-style-h2-em.width-default {
  max-width: 12ch;
  font-size: 4em;
  font-weight: 500;
  line-height: 1.06;
}

.heading-style-h2-em.width-default.v2 {
  max-width: none;
  font-size: 3em;
}

.text-size-medium-rem {
  font-size: 20px;
}

.text-size-medium-rem.text-weight-medium.text-style-muted-60 {
  opacity: .6;
}

.ativos_left_content-wrapper {
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  height: auto;
  margin-bottom: 208px;
  display: flex;
  position: sticky;
  top: 0;
  bottom: 0;
}

.intro_text-wrapper {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  text-align: center;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  max-width: none;
  margin-top: 0;
  display: flex;
  position: relative;
  top: -28px;
}

.text-size-xmedium-rem {
  font-size: 18px;
}

.text-size-xmedium-rem.text-weight-medium {
  height: auto;
  margin-left: 0;
  padding-left: 0;
  font-size: 1.2rem;
}

.text-size-xmedium-rem.text-weight-medium.contador {
  line-height: .8;
}

.drop_header_are-wrapper {
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  display: flex;
}

.hide {
  display: none;
}

.text-info_wrapper {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  text-align: center;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
  display: flex;
  position: absolute;
  inset: auto 0% 50%;
}

.patrimonio_content-wrapper {
  z-index: 999;
  padding-top: 0;
  padding-bottom: 32px;
  position: static;
}

.patrimonio_value_input {
  justify-content: center;
  align-items: center;
  margin-top: 0;
  margin-bottom: 32px;
  display: flex;
  position: relative;
}

.input_currency-wrapper {
  justify-content: flex-start;
  align-items: center;
  display: flex;
  position: relative;
}

.page-wrapper.calc-variation {
  color: #000;
  background-color: #f4f3f1;
}

.patrimonio_interactive_item {
  background-color: #fff;
  flex-flow: column;
  justify-content: flex-start;
  min-height: 218px;
  max-height: 263px;
  padding: 24px;
  font-weight: 500;
  display: flex;
  position: relative;
  overflow: clip;
}

.arrow-effect {
  width: 1.25em;
  max-width: 1.25em;
  position: relative;
  overflow: clip;
}

.button-and-info {
  grid-column-gap: 1.25rem;
  grid-row-gap: 1.25rem;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  max-width: 100%;
  display: flex;
}

.drop_ativos_area-wrapper {
  border: 5px dashed #00000030;
  border-radius: 16px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  min-height: 398px;
  margin-top: 94px;
  padding: 32px;
  line-height: 1;
  display: flex;
  position: relative;
}

.patrimonio_interactive_content-wrapper {
  z-index: 99;
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  background-color: #fff;
  border-radius: 30px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  width: 70%;
  max-height: 80svh;
  padding-bottom: 0;
  display: grid;
  position: relative;
  overflow: auto;
}

.intro_interactive-wrapper {
  margin-bottom: 2.5em;
}

.heading-style-h1-em {
  color: #000;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
}

.text-xmedium-rem {
  font-size: 24px;
}

.text-size-small {
  font-size: .875rem;
}

.container-espec {
  width: 100%;
  max-width: 83em;
  margin-left: auto;
  margin-right: auto;
}

.container-espec.v2 {
  justify-content: space-between;
  align-items: stretch;
  display: flex;
}

.ativos_clean-button {
  color: #000;
  background-color: #3898ec00;
  padding: 0;
  font-size: 18px;
  font-weight: 500;
  line-height: 1;
}

.text-size-medium-em {
  font-size: 1.13em;
}

.text-size-medium-em.width-default {
  max-width: 22.75em;
  font-size: 1.13rem;
}

.heading-and-paragraph {
  grid-column-gap: 1.88rem;
  grid-row-gap: 1.88rem;
  flex-flow: column;
  display: flex;
}

.heading-and-paragraph._22 {
  margin-top: 14px;
}

.heading-and-paragraph.v3 {
  grid-column-gap: 14px;
  grid-row-gap: 14px;
}

.currency-input {
  color: #000;
  text-align: left;
  border: 1px #000;
  border-radius: .63rem;
  max-width: 100%;
  min-height: 7rem;
  margin-top: 0;
  margin-bottom: 0;
  padding: 0 0 0 93px;
  font-size: 3.5em;
  font-weight: 500;
  line-height: 1;
  overflow: clip;
}

.currency-input.individual {
  outline-offset: 0px;
  background-color: #f4f3f1;
  border-style: none;
  outline: 3px #0000;
  width: 100%;
  min-height: auto;
  padding: 14px 14px 14px 46px;
  font-size: 16px;
}

.global-styles {
  display: block;
  position: fixed;
  inset: 0% auto auto 0%;
}

.ativos_content-wrapper {
  flex-flow: row;
  justify-content: space-between;
  align-items: flex-start;
  height: 100svh;
  padding-top: 33px;
  padding-bottom: 32px;
  display: flex;
  position: relative;
}

.ativos_right_content-wrapper {
  flex-flow: column;
  justify-content: flex-start;
  align-items: stretch;
  min-width: 665px;
  max-width: 665px;
  height: 100%;
  padding-top: 0;
  display: flex;
}

.add_ativo_manual {
  border: 1px solid #0000;
  border-radius: 10px;
  width: -moz-fit-content;
  width: fit-content;
  margin-top: 0;
  margin-bottom: 0;
  padding: 18px 47px 18px 14px;
  font-size: 20px;
  font-weight: 500;
  line-height: 1;
  transition-property: all;
  transition-duration: .4s;
  transition-timing-function: cubic-bezier(.175, .088, 0, 1.691);
}

.add_ativo_manual:focus {
  border-style: none;
}

.add_ativo_manual.desativado {
  opacity: 0;
  padding-top: 0;
  font-size: 0;
  transform: scale(0);
}

.text-size-regular {
  font-size: 1rem;
}

.currency_buttons-wrapper {
  justify-content: space-between;
  margin-top: 56px;
  display: flex;
}

._1-section-calc-money {
  width: 100%;
  display: none;
}

.main-wrapper {
  justify-content: center;
  align-items: flex-start;
  display: flex;
  position: relative;
}

.main-wrapper.calc-variation {
  flex-flow: column;
  width: 100%;
  font-size: 1rem;
}

.ativo_alocated_top-wrapper {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 24px;
  line-height: 1;
  display: flex;
}

.section_form {
  height: 100svh;
  padding-bottom: 0;
  position: relative;
}

._2-section-calc-ativos {
  width: 100%;
  display: none;
}

._0-home-section-calc-intro {
  width: 100%;
}

.ativos_item {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  color: #000;
  background-color: #fff;
  border: 1px solid #0000;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  max-height: 46px;
  padding: 14px;
  font-size: 20px;
  font-weight: 500;
  line-height: 1;
  display: flex;
}

.ativos_item:last-child {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  justify-content: center;
  align-items: center;
}

.ativos_item.adicionar_ativo {
  transform: scale3d(1none, 1none, 1none);
  color: #fff;
  transform-style: preserve-3d;
  background-color: #c49725;
  transition: all .4s cubic-bezier(.175, .088, 0, 1.691);
}

.ativos_item.adicionar_ativo.desativado {
  opacity: 0;
  color: #fff0;
  background-color: #c4972500;
  padding: 0;
  font-size: 0;
  transform: scale(0);
}

.ativos_item.adicionar {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  border-color: #e9e9e9;
  border-radius: 500px;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  min-width: 60px;
  height: auto;
  min-height: 60px;
  padding: 0 8px;
  font-size: 20px;
  transition-property: all;
  transition-duration: .5s;
  transition-timing-function: cubic-bezier(.175, .088, 0, 1.691);
}

.ativos_item.adicionar:hover {
  color: #fff;
  background-color: #c49725;
  border-color: #c5c5c500;
}

.icon-1x1 {
  line-height: 1.5;
  display: flex;
}

.button-hero {
  grid-column-gap: .38rem;
  grid-row-gap: .38rem;
  color: #565656;
  text-align: center;
  background-color: #d19e1e;
  border-radius: .5rem;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: 3.7rem;
  padding: 1.5rem 1.25rem 1.5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1;
  text-decoration: none;
  transition: background-color .4s;
  display: flex;
}

.button-hero:hover {
  background-color: #ffc127;
}

.button-hero.variation {
  color: #fff;
  background-color: #c49725;
  width: auto;
  min-width: 20rem;
  max-width: 100%;
  margin-top: 0;
  padding: 1.25rem 1.06rem;
}

.button-hero.variation.money {
  margin-top: 3rem;
}

.button-hero.variation.money.v3 {
  margin-top: 8px;
}

.categoria-ativo {
  color: #0006;
  font-size: 15px;
}

.ativo_individual_input {
  text-align: center;
  border: 1px solid #000;
  width: 100%;
  padding-top: 14px;
  padding-bottom: 14px;
  padding-left: 14px;
  font-size: 16px;
  line-height: 1;
}

.money_content_right-wrapper {
  flex-flow: column;
  max-width: 30.8em;
  height: -moz-fit-content;
  height: fit-content;
  padding-top: 32px;
  display: flex;
  position: relative;
}

.text-size-large-em {
  font-size: 1.75em;
}

.text-size-large-em.width-default {
  max-width: 24ch;
}

.text-size-large-em.width-default.text-weight-medium.text-style-muted-60 {
  max-width: 32ch;
  font-family: Satoshi Variable, Arial, sans-serif;
  font-size: 1.6rem;
  font-weight: 500;
}

.text-size-large-em.width-default.text-weight-medium.text-style-muted {
  max-width: 32ch;
  font-size: 24px;
}

.ativos_main-list {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex-flow: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.patrimonio_top_content-wrapper {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 48px;
  display: flex;
}

.padding-top {
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
}

.max-width-full {
  width: 100%;
  max-width: none;
}

.fs-styleguide_label {
  color: #fff;
  background-color: #2d62ff;
  border-radius: .25rem;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: .25rem .75rem .3rem;
  font-weight: 600;
  display: flex;
}

.fs-styleguide_label.is-tag {
  background-color: #dd23bb;
}

.nav_menu_link {
  color: #fff;
  padding: 1rem;
}

.fs-styleguide_section-header {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  border-bottom: 1px solid #eee;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  padding-bottom: 3rem;
  line-height: 1.4;
  display: grid;
}

.padding-section-large {
  padding-top: 8rem;
  padding-bottom: 8rem;
}

.max-width-medium {
  width: 100%;
  max-width: 23.88em;
}

.icon-1x1-medium {
  width: 2rem;
  height: 2rem;
}

.fs-styleguide_hero-label {
  color: #000;
  text-transform: uppercase;
  background-color: #eee;
  border-radius: .25rem;
  padding: .25rem .375rem;
  font-size: .75rem;
  font-weight: 500;
  text-decoration: none;
}

.padding-custom3 {
  padding: 3.5rem;
}

.spacer-xxhuge {
  width: 100%;
  padding-top: 12rem;
}

.fs-styleguide_background {
  border: 1px solid #0000001a;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  display: flex;
}

.background-color-secondary {
  background-color: #2d62ff;
}

.padding-global {
  margin-top: auto;
  margin-bottom: auto;
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.overflow-visible {
  overflow: visible;
}

.fs-styleguide_header-block {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  place-items: center start;
  display: grid;
}

.overflow-hidden {
  overflow: hidden;
}

.pointer-events-none {
  pointer-events: none;
}

.margin-xsmall {
  margin: .5rem;
}

.icon-1x1-large {
  width: 2.5rem;
  height: 2.5rem;
}

.margin-horizontal {
  margin-top: 0;
  margin-bottom: 0;
}

.fs-styleguide_item-header {
  border-bottom: 1px solid #0000001a;
  width: 100%;
  padding-bottom: 2rem;
}

.padding-bottom {
  padding-top: 0;
  padding-left: 0;
  padding-right: 0;
}

.fs-styleguide_heading-header {
  font-size: 6rem;
}

.fs-styleguide_item-wrapper {
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  display: flex;
}

.text-weight-xbold {
  font-weight: 800;
}

.fs-styleguide_section {
  grid-column-gap: 6rem;
  grid-row-gap: 6rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  place-items: start;
  display: grid;
}

.fs-styleguide_section.is-vertical {
  grid-column-gap: 4rem;
  grid-row-gap: 4rem;
  grid-template-columns: 1fr;
}

.icon-1x1-small {
  flex: none;
  width: 1rem;
  height: 1rem;
}

.form_checkbox {
  flex-direction: row;
  align-items: center;
  margin-bottom: .5rem;
  padding-left: 0;
  display: flex;
}

.padding-small {
  padding: 1rem;
}

.padding-vertical {
  padding-left: 0;
  padding-right: 0;
}

.max-width-small {
  width: 100%;
  max-width: 17rem;
}

.pointer-events-auto {
  pointer-events: auto;
}

.text-color-alternate {
  color: #fff;
}

.padding-horizontal {
  padding-top: 0;
  padding-bottom: 0;
}

.spacer-medium {
  width: 100%;
  padding-top: 2rem;
}

.margin-custom1 {
  margin: 1.5rem;
}

.container-small {
  width: 100%;
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
}

.background-color-primary {
  color: #fff;
  background-color: #c4c85a;
}

.fs-styleguide_spacing-all {
  display: none;
}

.form_component {
  margin-bottom: 0;
}

.spacer-xxlarge {
  width: 100%;
  padding-top: 5rem;
}

.text-align-left {
  text-align: left;
}

.spacer-huge {
  width: 100%;
  padding-top: 6rem;
}

.text-style-strikethrough {
  text-decoration: line-through;
}

.margin-xxlarge {
  margin: 5rem;
}

.margin-small {
  margin: 1rem;
}

.text-align-center {
  text-align: center;
}

.heading-style-h6 {
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
}

.overflow-scroll {
  overflow: scroll;
}

.margin-tiny {
  margin: .125rem;
}

.padding-xhuge {
  padding: 8rem;
}

.icon-height-small {
  height: 1rem;
}

.fs-styleguide_header {
  color: #fff;
  background-color: #c4c85a;
  background-image: radial-gradient(circle at 100% 100%, #dd23bb40, #0000 40%), radial-gradient(circle at 0 100%, #2d62ff4d, #0000 60%);
}

.padding-xxhuge {
  padding: 12rem;
}

.text-color-primary {
  color: #000;
}

.padding-large {
  padding: 3rem;
}

.aspect-ratio-portrait {
  aspect-ratio: 2 / 3;
  object-fit: cover;
}

.button-group {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: wrap;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.z-index-1 {
  z-index: 1;
  position: relative;
}

.text-align-right {
  text-align: right;
}

.padding-section-small {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.aspect-ratio-landscape {
  aspect-ratio: 3 / 2;
  object-fit: cover;
}

.text-weight-normal {
  font-weight: 400;
}

.padding-custom1 {
  padding: 1.5rem;
}

.form_radio {
  flex-direction: row;
  align-items: center;
  margin-bottom: .5rem;
  padding-left: 0;
  display: flex;
}

.text-weight-light {
  font-weight: 300;
}

.fs-styleguide_classes {
  grid-column-gap: 1px;
  grid-row-gap: 1px;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.spacer-xlarge {
  width: 100%;
  padding-top: 4rem;
}

.padding-0 {
  padding: 0;
}

.overflow-auto {
  overflow: auto;
}

.text-style-italic {
  font-style: italic;
}

.max-width-xlarge {
  width: 100%;
  max-width: 64rem;
}

.margin-xxhuge {
  margin: 12rem;
}

.text-weight-semibold {
  font-weight: 600;
}

.padding-custom2 {
  padding: 2.5rem;
}

.fs-styleguide_2-col {
  grid-column-gap: 4rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  display: grid;
}

.fs-styleguide_2-col.is-align-start {
  align-items: start;
}

.max-width-xxlarge {
  width: 100%;
  max-width: 80rem;
}

.fs-styleguide_empty-box {
  z-index: -1;
  background-color: #2d40ea0d;
  border: 1px dashed #2d40ea;
  min-width: 3rem;
  height: 3rem;
  position: relative;
}

.text-size-tiny {
  font-size: .75rem;
}

.max-width-large {
  width: 100%;
  max-width: 48rem;
}

.text-style-muted {
  opacity: .5;
  margin-bottom: 0;
}

.text-color-secondary {
  color: #222;
}

.heading-style-h4 {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.4;
}

.margin-vertical {
  margin-left: 0;
  margin-right: 0;
}

.text-weight-medium {
  font-weight: 500;
}

.spacer-tiny {
  width: 100%;
  padding-top: .125rem;
}

.aspect-ratio-widescreen {
  aspect-ratio: 16 / 9;
  object-fit: cover;
}

.margin-large {
  margin: 3rem;
}

.margin-0 {
  margin: 0;
}

.icon-height-large {
  height: 3rem;
}

.margin-xxsmall {
  margin: .25rem;
}

.form_message-success {
  color: #114e0b;
  background-color: #cef5ca;
  padding: 1.25rem;
}

.aspect-ratio-square {
  aspect-ratio: 1;
  object-fit: cover;
}

.background-color-alternate {
  background-color: #fff;
}

.fs-styleguide_heading-medium {
  font-size: 4rem;
}

.margin-xlarge {
  margin: 4rem;
}

.margin-medium {
  margin: 2rem;
}

.padding-left {
  padding-top: 0;
  padding-bottom: 0;
  padding-right: 0;
}

.align-center {
  margin-left: auto;
  margin-right: auto;
}

.fs-styleguide_spacing {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  background-image: linear-gradient(to top, #2d40ea1a, #fff0);
  grid-template-rows: auto auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  place-content: start;
  place-items: start stretch;
  display: grid;
  position: relative;
}

.heading-style-h3 {
  font-size: 2rem;
  font-weight: 500;
  line-height: 1.2;
}

.margin-custom2 {
  margin: 2.5rem;
}

.nav_button {
  padding: 1rem;
}

.heading-style-h2 {
  font-size: 3rem;
  font-weight: 500;
  line-height: 1.2;
}

.text-weight-bold {
  color: #000;
  font-weight: 700;
}

.padding-medium {
  padding: 2rem;
}

.form_radio-icon {
  width: .875rem;
  height: .875rem;
  margin-top: 0;
  margin-left: 0;
  margin-right: .5rem;
}

.form_radio-icon.w--redirected-checked {
  border-width: .25rem;
  width: .875rem;
  height: .875rem;
}

.form_radio-icon.w--redirected-focus {
  width: .875rem;
  height: .875rem;
  box-shadow: 0 0 .25rem 0 #3898ec;
}

.fs-styleguide_background-space {
  width: 1px;
  height: 1px;
  margin: 5rem;
}

.padding-xxlarge {
  padding: 5rem;
}

.text-size-large {
  font-size: 1.5rem;
}

.form_message-error {
  color: #3b0b0b;
  background-color: #f8e4e4;
  margin-top: .75rem;
  padding: .75rem;
}

.padding-xsmall {
  padding: .5rem;
}

.spacer-xsmall {
  width: 100%;
  padding-top: .5rem;
}

.spacing-clean {
  margin: 0;
  padding: 0;
}

.fs-styleguide_4-col {
  grid-column-gap: 4rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  display: grid;
}

.button {
  color: #fff;
  text-align: center;
  background-color: #c4c85a;
  border-radius: .5rem;
  padding: .75rem 1.5rem;
  font-weight: 600;
}

.button.is-text {
  color: #000;
  background-color: #0000;
  border: 2px #0000;
  padding: 0;
  font-size: 1rem;
}

.button.is-secondary {
  color: #000;
  background-color: #0000;
  border: 1px solid #222;
}

.button.is-large {
  padding: 1rem 2rem;
}

.button.is-icon {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  display: flex;
}

.button.is-small {
  padding: .5rem 1.25rem;
}

.spacer-small {
  width: 100%;
  padding-top: 1rem;
}

.fs-styleguide_3-col {
  grid-column-gap: 4rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  width: 100%;
  display: grid;
}

.fs-styleguide_3-col.is-align-start {
  align-items: start;
}

.fs-styleguide_item {
  grid-column-gap: 1.125rem;
  grid-row-gap: 1.125rem;
  border-bottom: 1px solid #0000001a;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  place-content: start;
  place-items: start;
  padding-bottom: 3rem;
  display: grid;
  position: relative;
}

.fs-styleguide_item.is-stretch {
  justify-items: stretch;
}

.text-style-nowrap {
  white-space: nowrap;
}

.spacer-xhuge {
  width: 100%;
  padding-top: 14.7rem;
}

.margin-huge {
  margin: 6rem;
}

.nav_component {
  background-color: #000;
  width: 100%;
  padding-top: 1rem;
  padding-bottom: 1rem;
  position: relative;
  inset: 0% 0% auto;
}

.padding-xxsmall {
  padding: .25rem;
}

.z-index-2 {
  z-index: 2;
  position: relative;
}

.margin-top {
  margin-bottom: 0;
  margin-left: 0;
  margin-right: 0;
}

.margin-xhuge {
  margin: 8rem;
}

.text-style-allcaps {
  text-transform: uppercase;
}

.padding-huge {
  padding: 6rem;
}

.fs-styleguide_spacer-box {
  background-color: #2d40ea1a;
  border: 1px dashed #2d40ea;
  width: 100%;
  position: relative;
}

.form_input {
  background-color: #0000;
  border: 1px solid #eee;
  min-height: 3rem;
  margin-bottom: .75rem;
  padding: .5rem 1rem;
  font-size: 1rem;
}

.form_input::placeholder, .form_input.is-select-input {
  color: #222;
}

.form_input.is-text-area {
  min-height: 8rem;
  padding-top: .75rem;
  font-size: 1rem;
}

.max-width-xxsmall {
  width: 100%;
  max-width: 12rem;
}

.text-style-link {
  color: #2d62ff;
  text-decoration: underline;
}

.padding-tiny {
  padding: .125rem;
}

.padding-section-medium {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.margin-right {
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
}

.max-width-xsmall {
  width: 100%;
  max-width: 16rem;
}

.background-color-tertiary {
  background-color: #dd23bb;
}

.padding-xlarge {
  padding: 4rem;
}

.margin-left {
  margin-top: 0;
  margin-bottom: 0;
  margin-right: 0;
}

.fs-styleguide_row {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  flex-direction: row;
  grid-template-rows: auto;
  grid-template-columns: auto;
  grid-auto-columns: auto;
  grid-auto-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.spacer-large {
  padding-top: 3rem;
}

.container-medium {
  width: 100%;
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
}

.text-style-quote {
  border-left: .25rem solid #e2e2e2;
  margin-bottom: 0;
  padding: 0 1.25rem;
  font-size: 1.25rem;
  line-height: 1.5;
}

.spacer-xxsmall {
  width: 100%;
  padding-top: .25rem;
}

.fs-styleguide_1-col {
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  width: 100%;
  display: grid;
}

.fs-styleguide_message {
  color: #5e5515;
  background-color: #fcf8d8;
  border-radius: .25rem;
  padding: .25rem .5rem;
  font-size: .875rem;
}

.nav_logo {
  width: 10rem;
}

.margin-custom3 {
  margin: 3.5rem;
}

.layer {
  justify-content: center;
  align-items: center;
  position: absolute;
  inset: 0%;
}

.heading-style-h5 {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.5;
}

.heading-style-h1 {
  font-size: 4rem;
  font-weight: 500;
  line-height: 1.1;
}

.container-large {
  width: 100%;
  max-width: 76em;
  margin-left: auto;
  margin-right: auto;
}

.form_checkbox-icon {
  border-radius: .125rem;
  width: .875rem;
  height: .875rem;
  margin: 0 .5rem 0 0;
}

.form_checkbox-icon.w--redirected-checked {
  background-size: 90%;
  border-radius: .125rem;
  width: .875rem;
  height: .875rem;
  margin: 0 .5rem 0 0;
}

.form_checkbox-icon.w--redirected-focus {
  border-radius: .125rem;
  width: .875rem;
  height: .875rem;
  margin: 0 .5rem 0 0;
  box-shadow: 0 0 .25rem 0 #3898ec;
}

.nav_container {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
  display: flex;
}

.icon-height-medium {
  height: 2rem;
}

.padding-right {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
}

.margin-bottom {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
}

.text-hat {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: space-between;
  align-items: center;
  width: auto;
  margin-bottom: 14px;
  display: flex;
}

.interactive-cards-wrapper {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  margin-top: 60px;
  margin-bottom: 48px;
  display: flex;
}

.interactive-cards-item {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  display: flex;
}

.interactive-etapa {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  color: #0009;
  border: 1px #0000003d;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  min-width: 216px;
  padding: 8px;
  font-size: 18px;
  display: flex;
}

.interactive-main-item {
  background-color: #fff;
  border-radius: 10px;
  width: 100%;
  height: 150px;
}

.intro-top-text {
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 60ch;
  display: flex;
}

.top-header-navbar {
  z-index: 90;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  padding-left: 32px;
  padding-right: 32px;
  display: flex;
  position: fixed;
  inset: auto 0% 32px;
}

.ajuda-button {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  background-color: #fff;
  border: 1px solid #0000003d;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  min-width: 216px;
  padding: 8px;
  font-size: 14px;
  display: flex;
}

.main-site-reino {
  z-index: 60;
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  background-color: #fff;
  border: 1px solid #0000003d;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  min-width: 216px;
  padding: 8px;
  font-size: 14px;
  display: flex;
  position: relative;
  inset: auto 0 0 auto;
}

.reino-logo-back {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: flex-start;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
}

.back-button {
  grid-column-gap: 14px;
  grid-row-gap: 14px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.brl_tag {
  font-size: 3.5em;
  position: absolute;
  inset: auto auto auto 14px;
}

.brl_tag.v2 {
  font-size: 16px;
  inset: auto auto auto 14px;
}

.button-increase, .button-decrease {
  background-color: #fff;
  border-radius: 200px;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  display: flex;
}

.icon-decrease_asset, .icon_increase-asset {
  line-height: 1.5;
  display: flex;
}

.arrow-dropdown {
  justify-content: center;
  align-items: center;
  display: flex;
}

.text-block {
  color: #0000008f;
}

.chart_bar-key-text {
  color: #2e4341;
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1;
}

.chart_bar-key {
  text-align: center;
  padding: 11%;
  position: absolute;
  inset: auto 0% 0%;
}

.range-slider {
  font-size: 1em;
}

.chart_column-title {
  text-align: center;
  margin-top: .9em;
  line-height: 1.1;
}

.chart_column {
  width: 100%;
}

.slider-resultado {
  width: 100%;
  font-size: 1em;
  position: relative;
  overflow: hidden;
}

.chart_wrap {
  grid-column-gap: 1.25em;
  grid-row-gap: 2.5em;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  padding-top: 3.7em;
  display: grid;
}

.range-slider-css {
  position: fixed;
}

.range_heading {
  text-align: center;
  margin-bottom: 1em;
  font-weight: 500;
}

.container {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 100em;
  min-height: 100vh;
  margin-left: auto;
  margin-right: auto;
  padding: 5em 6em;
  display: flex;
}

.global-styles-2 {
  position: fixed;
  inset: 0% 0% auto;
}

.range_key {
  justify-content: space-between;
  align-items: center;
  margin-top: 1px;
  margin-bottom: 1px;
  padding-top: 0;
  display: flex;
}

.html {
  width: 0;
  height: 0;
  position: absolute;
  inset: 0% 0% auto;
  overflow: hidden;
}

.range_wrap {
  width: 100%;
  max-width: 40em;
  margin-top: 0;
  margin-left: auto;
  margin-right: auto;
}

.chart_bar {
  background-color: #d9ec7f;
  border-radius: 1.5em;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: auto 0% 0%;
}

.chart_bar-wrap {
  width: 100%;
  padding-top: 231%;
  position: relative;
}

.disabled {
  opacity: .4;
  pointer-events: none;
  cursor: not-allowed;
  transform: scale(.8);
}

.interative-arrow {
  opacity: 1;
  padding: 4px 8px;
  position: absolute;
  inset: auto 32px auto auto;
}

.style-grid-patrimonio {
  position: fixed;
}

.porcentagem-calculadora {
  color: #fff;
  background-color: #101010;
  border-radius: 20px;
  order: -1;
  margin-bottom: 0;
  padding: 4px 13px;
  font-size: 18px;
  font-weight: 700;
}

.active-produto-item {
  flex-flow: column;
  display: flex;
}

.active-produto-item.hide {
  display: block;
}

.disabled-produto-item {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  margin-top: 10px;
  display: flex;
}

.valor-produto {
  font-size: 32px;
}

.porcentagem-calculadora-disabled {
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 700;
}

.porcentgem-disabled-wrapper {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  background-color: #f4f3f1;
  border: 1px #000;
  border-radius: 20px;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: 24px;
  padding: 6px 8px;
  display: flex;
  position: relative;
  overflow: clip;
}

.cifrao {
  opacity: .6;
  font-size: 18px;
}

.cifrao.value {
  opacity: 1;
  font-size: 16px;
}

.valor-produto-disabled {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: center;
  align-items: center;
  margin-right: 0;
  display: flex;
}

.edit-icon {
  margin-left: auto;
  padding-left: 8px;
  position: relative;
  inset: 0 0 auto auto;
}

.pin-function {
  opacity: 0;
  -webkit-backdrop-filter: blur(9px);
  backdrop-filter: blur(9px);
  background-color: #ffffff5c;
  border: 1px solid #0000003d;
  border-radius: 5000px;
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  display: flex;
  position: absolute;
  inset: 19px 32px auto auto;
}

.pin-function.active {
  background-color: #f4f3f1;
  transition: all .3s;
  transform: scale(1.1);
}

.text-block-2 {
  font-size: 15px;
}

.percent-wrapper {
  grid-column-gap: 14px;
  grid-row-gap: 14px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.background-item-acao {
  background-color: #242020;
  width: 0%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.texto-item {
  z-index: 10;
  grid-column-gap: 14px;
  grid-row-gap: 14px;
  color: #fff;
  mix-blend-mode: difference;
  display: flex;
  position: relative;
}

.componente-alocao-float {
  z-index: 99999;
  background-color: #fff;
  border: 1px #000;
  border-radius: 10px;
  justify-content: space-between;
  align-items: stretch;
  width: 650px;
  min-width: auto;
  max-width: 100%;
  height: 234px;
  min-height: auto;
  max-height: none;
  margin-left: auto;
  margin-right: auto;
  display: none;
  position: fixed;
  inset: auto auto 56px 50%;
  overflow: clip;
  transform: translate(-50%);
  box-shadow: 0 -9px 20px #0003, 0 9px 19px #0003;
}

.componente-alocao-left {
  border-right: 1px solid #0000004a;
  width: 40%;
}

.componente-alocao-left.v2 {
  border-right-style: none;
  width: 100%;
  height: -moz-fit-content;
  height: fit-content;
}

.componente-alocao-right {
  flex-flow: column;
  width: 60%;
  height: 100%;
  padding-bottom: 0;
  display: flex;
  position: relative;
}

.componente-alocao-right.v2 {
  width: auto;
  display: none;
}

.componente-alocao-top_wrapper {
  border-bottom: 1px solid #0000004a;
  border-right: 1px #000;
  justify-content: space-between;
  align-items: center;
  padding: 14px 20px;
  display: flex;
}

.componente-alocao-top_wrapper.v2 {
  border-bottom-style: none;
  width: 100%;
  max-width: 100%;
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
}

.percent-patrimony_wrapper {
  background-color: #f4f3f1;
  border-radius: 48px;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  line-height: 1;
}

.percent-patrimony_wrapper.v2 {
  background-color: #fff;
}

.patrimonio_money_wrapper {
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 0;
}

.patrimonio-alocacao_wrapper {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  margin-right: 0;
  display: flex;
}

.patrimonio-restante {
  font-size: 32px;
}

.patrimonio-restante.v2 {
  font-size: 48px;
}

.patrimonio-total-value {
  font-size: 14px;
}

.patrimonio-total-value.v2 {
  font-size: 18px;
}

.patrimonio-alocacao_wrapper-total {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  opacity: .5;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  margin-top: 8px;
  margin-right: 0;
  line-height: 1;
  display: flex;
}

.total-value-inner-wrapper {
  display: flex;
}

.ia-button-alocacao {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  background-color: #f4f3f1;
  border-radius: 48px;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  line-height: 1;
  transition: all .3s;
  display: flex;
}

.ia-button-alocacao:hover, .ia-button-alocacao.ativo {
  color: #fff;
  background-color: #000;
}

.ia-button-alocacao.v2 {
  background-color: #fff;
}

.ia-button-alocacao.v2:hover {
  background-color: #000;
}

.graficos-distribuicao-ativos {
  justify-content: flex-start;
  align-items: flex-end;
  height: auto;
  margin-top: auto;
  padding-bottom: 8px;
  padding-left: 20px;
  display: flex;
  position: relative;
  inset: 0% 0% auto;
  overflow: clip;
}

.graficos-distribuicao-ativos.v2 {
  margin-top: 32px;
  top: auto;
}

.ativos-graficos-wrapper {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex: none;
  align-items: flex-end;
  width: 100%;
  padding-bottom: 8px;
  padding-right: 14px;
  display: flex;
  position: relative;
  overflow: auto;
}

.ativos-graficos-wrapper.v2 {
  padding-bottom: 24px;
}

.ativos-grafico-item {
  grid-column-gap: 14px;
  grid-row-gap: 14px;
  flex-flow: column;
  flex: none;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
}

.nome-porcentagem-item {
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
}

.barra-porcentagem-item {
  background-color: #000;
  border-radius: 4px;
  width: 17px;
  height: 45px;
}

.porcentagem-inner-item {
  color: #fff;
  background-color: #101010;
  border-radius: 4px 4px 0 0;
  padding: 4px;
}

.text-inner-item {
  color: #fff;
  background-color: #101010;
  border-radius: 4px;
  padding: 4px;
}

.icon-next {
  display: flex;
  position: absolute;
  inset: 36px 57px auto auto;
}

.ia-input_wrapper {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex-flow: column;
  margin-top: auto;
  margin-bottom: auto;
  padding-left: 20px;
  padding-right: 20px;
  display: flex;
}

.ia-input_wrapper.v2 {
  display: none;
}

.top_ia_wrapper {
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  display: flex;
}

.close-icon {
  display: flex;
  position: relative;
  inset: 0 0 auto auto;
}

.prompt-input {
  outline-offset: 0px;
  background-color: #f4f3f1;
  border: 1px #000;
  border-radius: 10px;
  outline: 3px #000;
  width: 80%;
  padding: 16px;
  font-size: 14px;
  line-height: 1;
}

.prompt-input:focus {
  border-style: none;
}

.prompt-input-send-wrapper {
  grid-column-gap: 14px;
  grid-row-gap: 14px;
  flex-flow: row;
  display: flex;
}

.process-prompt {
  color: #fff;
  background-color: #101010;
  border-radius: 4px;
  height: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.spacer-divider {
  height: 100svh;
  display: none;
}

.close-ia {
  background-color: #f0f0f000;
  padding: 0;
}

.patrimonio_itens_wrapper {
  background-color: #f4f3f1;
  justify-content: space-between;
  align-items: stretch;
  display: flex;
}

.patrimonio_menu_wrapper {
  flex-flow: column;
  justify-content: flex-start;
  align-items: stretch;
  width: 25%;
  display: flex;
}

.dropdown-subcategory {
  background-color: #f0f0f000;
  height: 100%;
}

.dropdown-toggle {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  height: 100%;
  padding: 0;
  display: flex;
}

.dropdown-toggle.w--open {
  padding-left: 0;
}

.dropdown-list {
  background-color: #fff;
  border: 1px solid #000;
  border-radius: 10px;
}

.dropdown-list.w--open {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  background-color: #fff;
  border: 1px solid #00000021;
  border-radius: 10px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 210px;
  min-width: 210px;
  margin-top: 18px;
  padding: 8px 0;
  display: flex;
  left: 0;
  right: auto;
}

.ativo-item-subcategory {
  color: #000;
  background-color: #3898ec00;
  border-bottom: 1px solid #0000001a;
  flex: none;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
  font-size: 17px;
  text-decoration: none;
}

.ativo-item-subcategory.last {
  border-bottom-style: none;
}

._4-section-dgm-canvas {
  width: 100%;
  position: relative;
}

.dgm-canvas-area {
  width: 100%;
  height: 100%;
}

.resultados_wrapper_content {
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  height: 100svh;
  display: flex;
}

.detalhes_main_content {
  background-color: #fff;
  border-radius: 10px;
  min-width: 338px;
  position: relative;
  top: 56px;
}

.detalhes_header_wrapper {
  line-height: 1;
}

.header-detalhes-text {
  margin-top: 0;
  font-size: 28px;
  font-weight: 600;
}

.view-details-button {
  border-top: 1px solid #000;
  justify-content: center;
  align-items: center;
  padding-top: 20px;
  padding-bottom: 20px;
  font-size: 18px;
  display: flex;
}

.text_contet_wrapper {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 16px 20px 20px;
  display: flex;
  position: relative;
}

.section_indicator {
  background-color: #000;
  width: 22px;
  height: 22px;
  position: absolute;
  inset: 25px 24px auto auto;
}

.section_indicator.ball {
  border-radius: 100%;
}

.dgm-canvas-main-container {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

@media screen and (max-width: 991px) {
  .money_content_left-wrapper {
    text-align: center;
    justify-content: center;
    align-items: center;
  }

  .money_content-wrapper {
    flex-flow: column;
    justify-content: flex-start;
    align-items: stretch;
    height: auto;
  }

  .intro_content-wrapper {
    padding-top: 96px;
  }

  .next-content.v2 {
    align-items: center;
    max-width: none;
    margin-top: 32px;
  }

  .next-content.vv {
    position: relative;
  }

  .heading-style-h2-em.width-default {
    text-align: center;
    max-width: 20ch;
    font-size: 32px;
    font-weight: 600;
    line-height: 1.3;
  }

  .text-size-medium-rem.text-weight-medium.text-style-muted-60 {
    opacity: .5;
  }

  .ativos_left_content-wrapper {
    justify-content: center;
    align-items: center;
    margin-bottom: 32px;
    position: relative;
    top: 0;
  }

  .intro_text-wrapper {
    width: 100%;
  }

  .text-size-xmedium-rem.text-weight-medium {
    height: auto;
  }

  .input_currency-wrapper {
    justify-content: center;
    align-items: center;
    display: flex;
    position: relative;
  }

  .patrimonio_interactive_item {
    overflow: visible;
  }

  .button-and-info {
    order: 2;
    margin-top: 32px;
  }

  .heading-style-h1-em {
    font-size: 32px;
  }

  .heading-and-paragraph {
    justify-content: center;
    align-items: center;
  }

  .heading-and-paragraph._22 {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    justify-content: center;
    align-items: center;
    margin-top: 24px;
  }

  .currency-input {
    color: #000;
    padding-left: 91px;
  }

  .ativos_content-wrapper {
    flex-flow: column;
    justify-content: flex-start;
    align-items: center;
  }

  .ativos_right_content-wrapper {
    padding-top: 0;
  }

  .currency_buttons-wrapper {
    margin-top: 32px;
  }

  .ativos_item {
    justify-content: center;
    align-items: center;
  }

  .button-hero.variation {
    margin-top: 0;
  }

  .money_content_right-wrapper {
    margin-left: auto;
    margin-right: auto;
  }

  .text-size-large-em {
    font-size: 20px;
  }

  .text-size-large-em.width-default.text-weight-medium.text-style-muted {
    max-width: 35ch;
  }

  .ativos_main-list {
    justify-content: center;
    align-items: center;
  }

  .patrimonio_top_content-wrapper {
    flex-flow: column;
    align-items: center;
  }

  .padding-top {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .padding-section-large {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .spacer-xxhuge {
    padding-top: 8rem;
  }

  .margin-horizontal {
    margin-top: 0;
    margin-bottom: 0;
  }

  .padding-bottom {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .fs-styleguide_heading-header {
    font-size: 4rem;
  }

  .fs-styleguide_section {
    grid-column-gap: 2.5rem;
    grid-template-columns: 1fr;
  }

  .padding-vertical {
    padding-left: 0;
    padding-right: 0;
  }

  .padding-horizontal {
    padding-top: 0;
    padding-bottom: 0;
  }

  .spacer-medium {
    padding-top: 1.5rem;
  }

  .spacer-xxlarge {
    padding-top: 4rem;
  }

  .spacer-huge {
    padding-top: 5rem;
  }

  .margin-xxlarge {
    margin: 4rem;
  }

  .padding-xhuge {
    padding: 6rem;
  }

  .padding-xxhuge {
    padding: 8rem;
  }

  .padding-large {
    padding: 2.5rem;
  }

  .spacer-xlarge {
    padding-top: 3rem;
  }

  .margin-xxhuge {
    margin: 8rem;
  }

  .fs-styleguide_2-col {
    grid-column-gap: 2rem;
    grid-row-gap: 2rem;
    grid-template-columns: 1fr;
  }

  .margin-vertical {
    margin-left: 0;
    margin-right: 0;
  }

  .margin-large {
    margin: 2.5rem;
  }

  .fs-styleguide_heading-medium {
    font-size: 3rem;
  }

  .margin-xlarge {
    margin: 3rem;
  }

  .margin-medium {
    margin: 1.5rem;
  }

  .padding-left {
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
  }

  .nav_button {
    color: #fff;
  }

  .padding-medium {
    padding: 1.5rem;
  }

  .padding-xxlarge {
    padding: 4rem;
  }

  .fs-styleguide_4-col, .fs-styleguide_3-col {
    grid-template-columns: 1fr;
  }

  .spacer-xhuge {
    padding-top: 6rem;
  }

  .margin-huge {
    margin: 5rem;
  }

  .margin-top {
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .margin-xhuge {
    margin: 6rem;
  }

  .padding-huge {
    padding: 5rem;
  }

  .padding-section-medium {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .margin-right {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
  }

  .hide-tablet {
    display: none;
  }

  .max-width-full-tablet {
    width: 100%;
    max-width: none;
  }

  .padding-xlarge {
    padding: 3rem;
  }

  .margin-left {
    margin-top: 0;
    margin-bottom: 0;
    margin-right: 0;
  }

  .spacer-large {
    padding-top: 2.5rem;
  }

  .fs-styleguide_1-col {
    grid-column-gap: 2rem;
    grid-row-gap: 2rem;
  }

  .padding-right {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
  }

  .margin-bottom {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .interactive-cards-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    order: 3;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-top: 56px;
    padding-left: 0;
    padding-right: 0;
    display: grid;
  }

  .interactive-etapa {
    font-size: 16px;
    font-weight: 600;
  }

  .interactive-main-item {
    height: 140px;
  }

  .top-header-navbar {
    padding-left: 24px;
    padding-right: 24px;
    position: absolute;
  }

  .main-site-reino {
    right: 24px;
  }

  .brl_tag {
    font-size: 3em;
    position: absolute;
    inset: auto auto auto 14px;
  }

  .button-increase, .button-decrease {
    width: 64px;
    height: 64px;
  }

  .icon-decrease_asset {
    width: 32px;
  }

  .icon_increase-asset {
    width: 28px;
  }

  .container {
    padding-left: 6vw;
    padding-right: 6vw;
  }
}

@media screen and (max-width: 767px) {
  .intro_content-wrapper {
    height: auto;
  }

  .heading-style-h2-em {
    font-size: 28px;
  }

  .heading-style-h2-em.width-default.v2 {
    font-size: 32px;
  }

  .patrimonio_interactive_content-wrapper {
    grid-template-columns: 1fr 1fr;
  }

  .text-xmedium-rem {
    font-size: 20px;
  }

  .currency-input {
    font-size: 3.2em;
  }

  .ativos_right_content-wrapper {
    min-width: 100%;
    max-width: 100%;
  }

  .currency_buttons-wrapper {
    margin-top: 32px;
  }

  .ativo_alocated_top-wrapper {
    flex-flow: column;
  }

  .ativos_item {
    font-size: 18px;
  }

  .button-hero.variation {
    font-size: 16px;
  }

  .ativos_main-list {
    max-width: 100%;
  }

  .padding-top {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .fs-styleguide_section-header {
    font-size: .875rem;
  }

  .padding-section-large {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .spacer-xxhuge {
    padding-top: 4.5rem;
  }

  .padding-global {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .margin-horizontal {
    margin-top: 0;
    margin-bottom: 0;
  }

  .padding-bottom {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .padding-vertical {
    padding-left: 0;
    padding-right: 0;
  }

  .max-width-small {
    max-width: 20rem;
  }

  .padding-horizontal {
    padding-top: 0;
    padding-bottom: 0;
  }

  .spacer-medium {
    padding-top: 1.25rem;
  }

  .spacer-xxlarge {
    padding-top: 3rem;
  }

  .spacer-huge {
    padding-top: 3.5rem;
  }

  .margin-xxlarge {
    margin: 3rem;
  }

  .heading-style-h6 {
    font-size: .75rem;
  }

  .padding-xhuge {
    padding: 4rem;
  }

  .padding-xxhuge {
    padding: 4.5rem;
  }

  .padding-large {
    padding: 1.5rem;
  }

  .max-width-full-mobile-landscape {
    width: 100%;
    max-width: none;
  }

  .padding-section-small {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .hide-mobile-landscape {
    display: none;
  }

  .spacer-xlarge {
    padding-top: 2rem;
  }

  .margin-xxhuge {
    margin: 4.5rem;
  }

  .heading-style-h4 {
    font-size: 1rem;
  }

  .margin-vertical {
    margin-left: 0;
    margin-right: 0;
  }

  .margin-large {
    margin: 1.5rem;
  }

  .fs-styleguide_heading-medium {
    font-size: 2rem;
  }

  .margin-xlarge {
    margin: 2rem;
  }

  .margin-medium {
    margin: 1.25rem;
  }

  .padding-left {
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
  }

  .heading-style-h3 {
    font-size: 1.5rem;
  }

  .heading-style-h2 {
    font-size: 2rem;
  }

  .padding-medium {
    padding: 1.25rem;
  }

  .padding-xxlarge {
    padding: 3rem;
  }

  .text-size-large {
    font-size: 1.25rem;
  }

  .text-style-nowrap {
    white-space: normal;
  }

  .spacer-xhuge {
    padding-top: 4rem;
  }

  .margin-huge {
    margin: 3.5rem;
  }

  .margin-top {
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .margin-xhuge {
    margin: 4rem;
  }

  .padding-huge {
    padding: 3.5rem;
  }

  .padding-section-medium {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .margin-right {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
  }

  .padding-xlarge {
    padding: 2rem;
  }

  .margin-left {
    margin-top: 0;
    margin-bottom: 0;
    margin-right: 0;
  }

  .spacer-large {
    padding-top: 1.5rem;
  }

  .heading-style-h5 {
    font-size: .875rem;
  }

  .heading-style-h1 {
    font-size: 2.5rem;
  }

  .padding-right {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
  }

  .margin-bottom {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .interactive-cards-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    flex-flow: column;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    margin-top: 32px;
    margin-bottom: 32px;
    display: grid;
  }

  .button-increase, .button-decrease {
    width: 64px;
    height: 64px;
  }

  .icon-decrease_asset, .icon_increase-asset {
    width: 32px;
  }

  .chart_wrap {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

@media screen and (max-width: 479px) {
  .padding-espec {
    padding-left: 24px;
    padding-right: 24px;
  }

  .counter_ativos {
    font-size: 16px;
  }

  .intro_content-wrapper {
    height: auto;
  }

  .ativos_counter-wrapper {
    flex-flow: column;
    justify-content: center;
    align-items: center;
    margin-left: 0;
    margin-right: auto;
  }

  .heading-style-h2-em.width-default {
    font-size: 28px;
  }

  .text-size-medium-rem.text-weight-medium.text-style-muted-60 {
    font-size: 15px;
  }

  .intro_text-wrapper {
    justify-content: center;
    align-items: stretch;
  }

  .text-size-xmedium-rem.text-weight-medium {
    height: auto;
    line-height: 1;
  }

  .text-size-xmedium-rem.text-weight-medium.contador {
    font-size: 16px;
  }

  .drop_header_are-wrapper {
    flex-flow: row;
    justify-content: space-between;
    align-items: flex-start;
  }

  .button-and-info {
    order: 2;
    margin-top: 32px;
  }

  .drop_ativos_area-wrapper {
    min-height: 451px;
    padding-top: 14px;
    padding-left: 14px;
    padding-right: 14px;
  }

  .heading-style-h1-em {
    font-size: 24px;
  }

  .text-xmedium-rem {
    font-size: 18px;
  }

  .text-xmedium-rem.text-weight-medium.descricao, .ativos_clean-button {
    font-size: 16px;
  }

  .currency-input {
    min-height: 4rem;
    padding-left: 52px;
    font-size: 28px;
  }

  .ativos_item {
    border-radius: 6px;
    max-height: 32px;
    font-size: 14px;
  }

  .button-hero {
    font-size: 1rem;
  }

  .button-hero.variation {
    width: 100%;
    min-width: auto;
  }

  .text-size-large-em {
    font-size: 18px;
  }

  .ativos_main-list {
    grid-column-gap: 14px;
    grid-row-gap: 14px;
  }

  .padding-top {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .margin-horizontal {
    margin-top: 0;
    margin-bottom: 0;
  }

  .padding-bottom {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .padding-vertical {
    padding-left: 0;
    padding-right: 0;
  }

  .max-width-small {
    text-align: center;
    max-width: 100%;
  }

  .padding-horizontal {
    padding-top: 0;
    padding-bottom: 0;
  }

  .margin-vertical {
    margin-left: 0;
    margin-right: 0;
  }

  .max-width-full-mobile-portrait {
    width: 100%;
    max-width: none;
  }

  .padding-left {
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
  }

  .button.is-text {
    padding: 0;
    font-size: 1rem;
  }

  .margin-top {
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .margin-right {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
  }

  .hide-mobile-portrait {
    display: none;
  }

  .margin-left {
    margin-top: 0;
    margin-bottom: 0;
    margin-right: 0;
  }

  .fs-styleguide_row {
    flex-wrap: wrap;
  }

  .padding-right {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
  }

  .margin-bottom {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .interactive-cards-wrapper {
    order: 3;
    padding-left: 0;
    padding-right: 0;
    display: flex;
  }

  .interactive-cards-item {
    width: 100%;
  }

  .intro-top-text {
    order: -1;
  }

  .top-header-navbar {
    justify-content: center;
    align-items: center;
    position: absolute;
  }

  .main-site-reino {
    z-index: 60;
  }

  .brl_tag {
    font-size: 24px;
  }

  .button-increase, .button-decrease {
    width: 56px;
    height: 56px;
  }

  .icon-decrease_asset, .icon_increase-asset {
    width: 28px;
  }
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031778-d41bd7a2 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031797-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d03179c-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0317a1-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0317a6-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0317ac-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0317b2-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0317b8-d41bd7a2 {
  justify-self: start;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d03189a-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0318a2-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0318b9-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0318d2-d41bd7a2 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d0318d5-d41bd7a2 {
  justify-self: start;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d0318d7-d41bd7a2 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d0318da-d41bd7a2 {
  justify-self: start;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d03192a-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031934-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031935-d41bd7a2 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d0319a7-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319ac-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319b1-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319b6-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319bb-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319c0-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319ca-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319cf-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319d4-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319d9-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319de-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319e3-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319e8-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319ed-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319f2-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319f7-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d0319fc-d41bd7a2 {
  justify-self: start;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031a00-d41bd7a2 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031a01-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a06-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a0b-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a10-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a24-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a29-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a2e-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a33-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a38-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a3d-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a47-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a4c-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a51-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a56-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a5b-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a60-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a65-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a6a-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a6f-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a74-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a79-d41bd7a2 {
  justify-self: start;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031a7d-d41bd7a2 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031a7e-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a83-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a88-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a8d-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031a9e-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031aa3-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031aa8-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031aad-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031ab2-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031ab7-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031abc-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031ac1-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031ac6-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031acb-d41bd7a2 {
  justify-self: start;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031acf-d41bd7a2 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031ad0-d41bd7a2 {
  justify-self: start;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031ad3-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031b54-d41bd7a2, #w-node-a51d25c8-e231-1c18-5c59-a8215d031b81-d41bd7a2 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-a51d25c8-e231-1c18-5c59-a8215d031bbf-d41bd7a2 {
  justify-self: start;
}

@media screen and (max-width: 767px) {
  #w-node-_465990d1-5f58-426f-4cb3-920c1690ac11-9f7c43af {
    grid-area: span 1 / span 2 / span 1 / span 2;
  }
}


@font-face {
  font-family: 'Satoshi Variable';
  src: url('../fonts/Satoshi-Variable.woff2') format('woff2');
  font-weight: 300 900;
  font-style: normal;
  font-display: swap;
}