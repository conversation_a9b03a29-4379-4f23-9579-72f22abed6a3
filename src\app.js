/**
 * Main Application
 * Integrates all extracted JavaScript modules and initializes them
 */
import { AttributeFixerSystem } from './modules/attribute-fixer.js';
import { ChartAnimationSystem } from './modules/chart-animation.js';
import { CurrencyControlSystem } from './modules/currency-control.js';
import { CurrencyFormattingSystem } from './modules/currency-formatting.js';
import { eventCoordinator } from './modules/event-coordinator.js';
import { MotionAnimationSystem } from './modules/motion-animation.js';
import { PatrimonySyncSystem } from './modules/patrimony-sync.js';
import { ProductSystem } from './modules/product-system.js';
import { ScrollFloatAnimationSystem } from './modules/scroll-float-animation.js';
import { SectionVisibilitySystem } from './modules/section-visibility.js';
import { SimpleSyncSystem } from './modules/simple-sync.js';
import { StepNavigationSystem } from './modules/step-navigation.js';

/**
 * Main Application Class
 * Coordinates initialization and management of all systems
 */
class ReinoCalculatorApp {
  constructor() {
    // Initialize EventCoordinator first
    this.eventCoordinator = eventCoordinator;
    this.systems = {
      currencyControl: new CurrencyControlSystem(),
      currencyFormatting: new CurrencyFormattingSystem(),
      motionAnimation: new MotionAnimationSystem(),
      productSystem: new ProductSystem(),
      sectionVisibility: new SectionVisibilitySystem(),
      patrimonySync: new PatrimonySyncSystem(),
      chartAnimation: new ChartAnimationSystem(),
      simpleSync: new SimpleSyncSystem(),
      attributeFixer: new AttributeFixerSystem(),
      scrollFloatAnimation: new ScrollFloatAnimationSystem(),
      stepNavigation: new StepNavigationSystem(),
    };

    this.isInitialized = false;
    this.initializationOrder = [
      'attributeFixer', // Fix attribute inconsistencies FIRST
      'currencyFormatting', // Base currency system first
      'patrimonySync', // Core patrimony synchronization BEFORE currency controls
      'currencyControl', // Currency controls AFTER patrimony sync
      'simpleSync', // Simple synchronization for visual bars
      'chartAnimation', // Chart animations
      'motionAnimation', // Motion effects
      'productSystem', // Product interactions
      'scrollFloatAnimation', // Scroll-triggered float animations
      'sectionVisibility', // Section visibility control
      'stepNavigation', // Step navigation system
    ];
  }

  /**
   * Initialize all systems in the correct order
   */
  async init() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize systems in order
      for (const systemName of this.initializationOrder) {
        const system = this.systems[systemName];
        if (system && typeof system.init === 'function') {
          await system.init();
        }
      }

      this.isInitialized = true;
      this.setupGlobalAPI();
      this.setupErrorHandling();

      // Dispatch ready event
      document.dispatchEvent(
        new CustomEvent('reinoCalculatorReady', {
          detail: {
            app: this,
            systems: this.systems,
          },
        })
      );
    } catch (error) {
      // Silent error handling
    }
  }

  /**
   * Setup global API for debugging and external access
   */
  setupGlobalAPI() {
    window.ReinoCalculator = {
      app: this,
      systems: this.systems,

      // Utility methods
      restart: () => this.restart(),
      getSystemStatus: () => this.getSystemStatus(),

      // Debug utilities
      debugSync: () => {
        console.log('🔍 REINO CALCULATOR - SYNCHRONIZATION DEBUG');
        console.log('='.repeat(60));

        if (this.systems.simpleSync) {
          this.systems.simpleSync.debugFullStatus();
        } else {
          console.error('❌ SimpleSyncSystem not available');
        }

        if (this.systems.patrimonySync) {
          console.log('\n💰 PATRIMONY SYNC STATUS:');
          console.log(`  Main value: R$ ${this.systems.patrimonySync.getMainValue()}`);
          console.log(`  Total allocated: R$ ${this.systems.patrimonySync.getTotalAllocated()}`);
          console.log(`  Remaining: R$ ${this.systems.patrimonySync.getRemainingValue()}`);
        }

        if (this.systems.scrollFloatAnimation) {
          console.log('\n🎬 SCROLL FLOAT ANIMATION STATUS:');
          const status = this.systems.scrollFloatAnimation.getStatus();
          console.log('  isAnimated:', status.isAnimated, '| hasInView:', status.hasInView);
        }

        console.log('\n🎯 Quick Commands:');
        console.log('  • ReinoCalculator.debugSync() - Full sync analysis');
        console.log('  • ReinoCalculator.animation.scrollFloat.forceShow() - Force show float');
        console.log('  • ReinoCalculator.data.sync.debugPairings() - Pairing details');
        console.log('  • ReinoCalculator.data.sync.debugUnpairedElements() - Find orphans');
        console.log('  • ReinoCalculator.data.sync.debugSyncTest() - Test synchronization');
        console.log('  • ReinoCalculator.data.sync.debugCacheSync() - Cache restoration analysis');
        console.log(
          '  • ReinoCalculator.data.sync.debugZeroingIssues() - Detect visual zeroing problems'
        );
        console.log(
          '  • ReinoCalculator.data.sync.forceSyncFromSliders() - Force sync visuals from sliders'
        );
      },

      // Quick fix for zeroing issues
      fixZeroing: () => {
        if (this.systems.simpleSync) {
          this.systems.simpleSync.forceSyncFromSliders();
        } else {
          console.error('❌ SimpleSyncSystem not available');
        }
      },

      // System controls
      currency: {
        control: this.systems.currencyControl,
        formatting: this.systems.currencyFormatting,
      },

      animation: {
        motion: this.systems.motionAnimation,
        chart: this.systems.chartAnimation,
        scrollFloat: this.systems.scrollFloatAnimation,
      },

      ui: {
        products: this.systems.productSystem,
        visibility: this.systems.sectionVisibility,
      },

      data: {
        patrimony: this.systems.patrimonySync,
        sync: this.systems.simpleSync,
        attributeFixer: this.systems.attributeFixer,
      },

      navigation: {
        stepNavigation: this.systems.stepNavigation,
      },
    };
  }

  /**
   * Setup global error handling
   */
  setupErrorHandling() {
    window.addEventListener('error', (event) => {
      // console.error('Reino Calculator Error:', event.error);
    });

    window.addEventListener('unhandledrejection', (event) => {
      // console.error('Reino Calculator Unhandled Promise Rejection:', event.reason);
    });
  }

  /**
   * Get status of all systems
   */
  getSystemStatus() {
    const status = {};

    for (const [name, system] of Object.entries(this.systems)) {
      status[name] = {
        initialized: system.isInitialized || false,
        available: typeof system.init === 'function',
      };
    }

    return status;
  }

  /**
   * Restart the application
   */
  async restart() {
    // console.log('Restarting Reino Calculator App...');

    // Cleanup existing systems
    for (const system of Object.values(this.systems)) {
      if (typeof system.cleanup === 'function') {
        system.cleanup();
      }
    }

    this.isInitialized = false;

    // Reinitialize
    await this.init();
  }

  /**
   * Cleanup all systems
   */
  cleanup() {
    for (const system of Object.values(this.systems)) {
      if (typeof system.cleanup === 'function') {
        system.cleanup();
      }
    }

    this.isInitialized = false;
    delete window.ReinoCalculator;

    // console.log('Reino Calculator App cleaned up');
  }
}

// Create and initialize the application
const app = new ReinoCalculatorApp();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => app.cleanup());

// Export for manual control if needed
export { ReinoCalculatorApp };
