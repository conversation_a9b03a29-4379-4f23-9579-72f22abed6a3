# Project Documentation

This directory contains comprehensive documentation for the project, with special focus on the Webflow template policy and development workflows.

## 📚 Documentation Index

### Core Policies
- [**WEBFLOW_POLICY.md**](../WEBFLOW_POLICY.md) - **READ THIS FIRST** - Critical read-only policy for Webflow template folder
- [WEBFLOW_FILE_PATTERNS.md](./WEBFLOW_FILE_PATTERNS.md) - Technical reference for Webflow export file structures

### Webflow Template Folder
- [Modelo - Webflow/README.md](../Modelo%20-%20Webflow/README.md) - Direct folder warning and guidelines

### Development
- [Project README](../README.md) - Main project setup and development guide
- Package Scripts - See `package.json` for available commands

## 🚨 Quick Reference: Webflow Policy

### ❌ NEVER DO
- Edit files in `Modelo - Webflow/` directly
- Add/remove files from Webflow folder manually
- Modify HTML, CSS, or JS files in the template folder

### ✅ CORRECT WORKFLOW
1. Make changes in **Webflow platform**
2. **Export** updated files from Webflow
3. **Replace entire** `Modelo - Webflow` folder
4. **Commit** the complete replacement

## 🔧 Available Commands

```bash
# Validate Webflow policy compliance
npm run validate:webflow

# Standard development commands
npm run dev          # Development server
npm run build        # Production build
npm run lint         # Code linting
npm run test         # Run tests
```

## 📁 Documentation Structure

```
docs/
├── README.md                    # This file - Documentation index
├── WEBFLOW_FILE_PATTERNS.md     # Technical file patterns reference
└── [future documentation]      # Additional docs as needed

../
├── WEBFLOW_POLICY.md           # Main policy document
├── Modelo - Webflow/
│   └── README.md               # Folder-specific warnings
└── README.md                   # Project main documentation
```

## 🎯 Getting Started

1. **Read the Webflow Policy**: Start with [WEBFLOW_POLICY.md](../WEBFLOW_POLICY.md)
2. **Understand File Patterns**: Review [WEBFLOW_FILE_PATTERNS.md](./WEBFLOW_FILE_PATTERNS.md)
3. **Follow Development Workflow**: See [Project README](../README.md)
4. **Validate Setup**: Run `npm run validate:webflow`

## 🔍 Need Help?

- **Webflow Changes**: Use Webflow platform, then export
- **Policy Questions**: Review [WEBFLOW_POLICY.md](../WEBFLOW_POLICY.md)
- **Technical Issues**: Check validation with `npm run validate:webflow`
- **File Structure**: Refer to [WEBFLOW_FILE_PATTERNS.md](./WEBFLOW_FILE_PATTERNS.md)

## 📝 Contributing to Documentation

When updating documentation:
- Keep policy documents in sync
- Update file patterns when Webflow structure changes
- Maintain clear cross-references between documents
- Test validation scripts after changes

---

**🔗 Remember: Webflow platform is the source of truth for all template changes**