# WEBFLOW FILE PATTERNS REFERENCE

This document describes the typical file patterns and structures found in Webflow exports to help with validation and understanding of the `Modelo - Webflow` folder.

## 📁 Standard Webflow Export Structure

```
Modelo - Webflow/
├── index.html                 # Main page (always present)
├── [page-name].html           # Additional pages (variable)
├── css/
│   ├── normalize.css          # CSS reset (Webflow standard)
│   ├── webflow.css           # Webflow framework styles
│   └── [project-name].css    # Custom project styles
├── js/
│   ├── jquery-3.5.1.min.js  # jQuery library (version may vary)
│   └── webflow.js            # Webflow interactions
├── images/
│   ├── *.svg                 # Vector graphics
│   ├── *.png                 # Raster images
│   ├── *.jpg / *.jpeg        # Compressed images
│   └── *.webp                # Modern image format
└── fonts/
    ├── *.woff                # Web font format
    ├── *.woff2               # Compressed web font
    ├── *.ttf                 # TrueType fonts
    └── *.otf                 # OpenType fonts
```

## 🔍 File Pattern Validation Rules

### HTML Files
- **Pattern**: `*.html`
- **Required**: At least `index.html`
- **Common names**: 
  - `index.html` (homepage)
  - `[page-slug].html` (other pages)
  - `404.html` (error page, if configured)
- **Characteristics**:
  - Contains Webflow-generated HTML structure
  - Includes Webflow CSS and JS references
  - Has Webflow-specific class names and attributes

### CSS Directory
- **Path**: `css/`
- **Required files**:
  - `normalize.css` - CSS reset/normalize
  - `webflow.css` - Webflow framework styles
  - `[project-name].css` - Custom styles
- **Characteristics**:
  - Minified in production exports
  - Contains Webflow-specific class naming conventions
  - Includes responsive breakpoint definitions

### JavaScript Directory
- **Path**: `js/`
- **Common files**:
  - `jquery-[version].min.js` - jQuery library
  - `webflow.js` - Webflow interactions and animations
- **Optional files**:
  - Custom JavaScript files (if added in Webflow)
  - Third-party libraries

### Images Directory
- **Path**: `images/`
- **Supported formats**:
  - `.svg` - Vector graphics, icons
  - `.png` - Images with transparency
  - `.jpg` / `.jpeg` - Compressed photos
  - `.webp` - Modern efficient format
  - `.gif` - Animated images (rare)
- **Naming**: Webflow generates optimized filenames

### Fonts Directory
- **Path**: `fonts/`
- **Supported formats**:
  - `.woff2` - Primary web font format
  - `.woff` - Fallback web font format
  - `.ttf` - TrueType fonts
  - `.otf` - OpenType fonts
- **Usage**: Only included if custom fonts are used

## 🚨 Violation Detection Patterns

### Indicators of Direct Editing (VIOLATIONS)
- Modified timestamps on individual files without corresponding Webflow export
- Changes to core files like `normalize.css` or `webflow.css`
- Addition of non-Webflow files or directories
- Custom HTML comments or scripts inserted manually
- Modified file structure outside Webflow conventions

### Legitimate Change Patterns
- Complete folder replacement with new timestamp
- Addition/removal of entire page files
- Bulk changes to images or assets
- Updated file versions (e.g., `jquery-3.5.1.min.js` → `jquery-3.6.0.min.js`)

## 📊 Validation Criteria

### File Integrity Checks
1. **Required Files**: `index.html`, `css/` directory
2. **Structure Consistency**: Standard Webflow folder organization
3. **File Extensions**: Only Webflow-supported formats
4. **Naming Conventions**: Webflow-style naming patterns

### Content Validation
1. **HTML Structure**: Contains Webflow doctype and meta tags
2. **CSS Links**: References to Webflow CSS files
3. **JavaScript**: Webflow.js inclusion and configuration
4. **Asset References**: Proper relative path structure

## 🔧 Automated Validation Script Usage

The validation script checks for:
- Presence of required files and directories
- Proper file extensions and naming
- Basic content structure validation
- Detection of non-Webflow modifications

### Example Validation Command
```bash
npm run validate:webflow
```

### Expected Output
```
✅ Webflow folder structure is valid
✅ Required files are present
✅ File patterns match Webflow conventions
✅ No unauthorized modifications detected
```

## 📝 Change Detection Guidelines

### Safe Changes (Normal Webflow Export)
- Complete folder replacement
- New page additions with corresponding HTML files
- Asset updates (images, fonts) in bulk
- Version updates to Webflow-managed files

### Suspicious Changes (Potential Violations)
- Single file modifications
- Changes to core CSS/JS files
- Addition of non-Webflow files
- Structural changes to folder organization

## 🛠️ Maintenance Notes

### For Developers
- Use this reference to understand Webflow export structure
- Validate changes against these patterns
- Report deviations that might indicate policy violations

### For Project Maintainers
- Update this document when Webflow changes export structure
- Add new file patterns as Webflow introduces features
- Maintain validation scripts based on these patterns

## 📚 Related Documentation

- [WEBFLOW_POLICY.md](../WEBFLOW_POLICY.md) - Main policy document
- [Modelo - Webflow/README.md](../Modelo%20-%20Webflow/README.md) - Folder-specific warnings
- [Webflow Documentation](https://webflow.com/help) - Official Webflow help

---

**Note**: File patterns may vary based on Webflow plan, features used, and export settings. This document represents common patterns and should be updated as needed.