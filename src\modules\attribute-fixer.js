/**
 * Attribute Fixer System
 * Runtime correction of attribute inconsistencies in Webflow elements
 * This system fixes critical pairing issues without modifying the source Webflow files
 */

export class AttributeFixerSystem {
  constructor() {
    this.isInitialized = false;
    this.fixesApplied = [];
    this.originalAttributes = new Map(); // Store original values for debugging
  }

  async init() {
    try {
      console.log('🔧 AttributeFixerSystem: Starting runtime corrections...');

      // Store original attributes before making changes
      this.backupOriginalAttributes();

      // Apply all fixes
      this.fixCapitalizationIssues();
      this.fixTypos();
      this.fixCategoryAssignments();
      this.fixAttributeOrder();
      this.fixDuplicates();

      this.isInitialized = true;

      console.log(`✅ AttributeFixerSystem: Applied ${this.fixesApplied.length} fixes`);
      this.logFixesSummary();

    } catch (error) {
      console.error('❌ AttributeFixerSystem initialization failed:', error);
    }
  }

  backupOriginalAttributes() {
    // Backup patrimonio elements
    const patrimonioElements = document.querySelectorAll('.patrimonio_interactive_item[ativo-category][ativo-product]');
    patrimonioElements.forEach((el, index) => {
      this.originalAttributes.set(`patrimonio-${index}`, {
        category: el.getAttribute('ativo-category'),
        product: el.getAttribute('ativo-product'),
        element: el
      });
    });

    // Backup ativos elements
    const ativosElements = document.querySelectorAll('.ativos-grafico-item[ativo-category][ativo-product]');
    ativosElements.forEach((el, index) => {
      this.originalAttributes.set(`ativos-${index}`, {
        category: el.getAttribute('ativo-category'),
        product: el.getAttribute('ativo-product'),
        element: el
      });
    });

    console.log(`📋 Backed up attributes for ${this.originalAttributes.size} elements`);
  }

  fixCapitalizationIssues() {
    console.log('🔧 Fixing capitalization issues...');

    // Fix "Fundo de Investimento" → "Fundo de investimento"
    this.applyAttributeFix(
      '.patrimonio_interactive_item[ativo-category="Fundo de Investimento"]',
      'ativo-category',
      'Fundo de investimento',
      'Capitalization: Fundo de Investimento → Fundo de investimento'
    );

    // Fix "Renda Variável" → "Renda variável"
    this.applyAttributeFix(
      '.patrimonio_interactive_item[ativo-category="Renda Variável"]',
      'ativo-category',
      'Renda variável',
      'Capitalization: Renda Variável → Renda variável'
    );
  }

  fixTypos() {
    console.log('🔧 Fixing typos...');

    // Fix "Popupança" → "Poupança"
    this.applyAttributeFix(
      '.patrimonio_interactive_item[ativo-product="Popupança"]',
      'ativo-product',
      'Poupança',
      'Typo fix: Popupança → Poupança'
    );
  }

  fixCategoryAssignments() {
    console.log('🔧 Fixing category assignments...');

    // Fix "Operação compromissada" category for "Criptoativos" → should be "Outros"
    this.applyAttributeFix(
      '.patrimonio_interactive_item[ativo-category="Operação compromissada"][ativo-product="Criptoativos"]',
      'ativo-category',
      'Outros',
      'Category fix: Criptoativos moved from "Operação compromissada" to "Outros"'
    );
  }

  fixAttributeOrder() {
    console.log('🔧 Standardizing attribute order...');

    // Find elements with ativo-product before ativo-category and reorder them
    const elementsToReorder = document.querySelectorAll('[ativo-product][ativo-category]');

    elementsToReorder.forEach((el) => {
      const category = el.getAttribute('ativo-category');
      const product = el.getAttribute('ativo-product');

      if (category && product) {
        // Remove both attributes
        el.removeAttribute('ativo-category');
        el.removeAttribute('ativo-product');

        // Add them back in correct order (category first)
        el.setAttribute('ativo-category', category);
        el.setAttribute('ativo-product', product);

        this.fixesApplied.push(`Attribute order: Standardized order for ${category} + ${product}`);
      }
    });
  }

  fixDuplicates() {
    console.log('🔧 Handling duplicate elements...');

    // Find duplicate ativos elements
    const ativosElements = document.querySelectorAll('.ativos-grafico-item[ativo-category][ativo-product]');
    const seen = new Set();
    const duplicates = [];

    ativosElements.forEach((el) => {
      const category = el.getAttribute('ativo-category');
      const product = el.getAttribute('ativo-product');
      const key = `${category}|${product}`;

      if (seen.has(key)) {
        duplicates.push({ element: el, category, product });
      } else {
        seen.add(key);
      }
    });

    // Handle the specific duplicate: "Fundo de investimento" + "Ações"
    duplicates.forEach((duplicate) => {
      if (duplicate.category === 'Fundo de investimento' && duplicate.product === 'Ações') {
        // Change the duplicate to "Renda variável" + "Ações" to match the patrimonio element
        duplicate.element.setAttribute('ativo-category', 'Renda variável');
        this.fixesApplied.push(`Duplicate fix: Changed duplicate "Fundo de investimento + Ações" to "Renda variável + Ações"`);
      }
    });
  }

  applyAttributeFix(selector, attribute, newValue, description) {
    const elements = document.querySelectorAll(selector);

    elements.forEach((el) => {
      const oldValue = el.getAttribute(attribute);
      el.setAttribute(attribute, newValue);
      this.fixesApplied.push(`${description} (${selector})`);

      console.log(`  ✅ ${oldValue} → ${newValue}`);
    });

    if (elements.length === 0) {
      console.warn(`  ⚠️ No elements found for selector: ${selector}`);
    }
  }

  logFixesSummary() {
    console.log('\n📋 ATTRIBUTE FIXES SUMMARY:');
    console.log('=' .repeat(50));

    this.fixesApplied.forEach((fix, index) => {
      console.log(`${index + 1}. ${fix}`);
    });

    console.log('\n🎯 EXPECTED IMPROVEMENTS:');
    console.log('✅ Capitalization mismatches resolved');
    console.log('✅ Typos corrected');
    console.log('✅ Category assignments fixed');
    console.log('✅ Duplicate elements handled');
    console.log('✅ Attribute order standardized');

    console.log('\n🔍 To verify fixes worked:');
    console.log('  • Run: ReinoCalculator.debugSync()');
    console.log('  • Check: Should see 15/15 elements paired (100% success rate)');
  }

  // Validation method to check if fixes worked
  validateFixes() {
    console.log('🔍 Validating applied fixes...');

    const patrimonioElements = document.querySelectorAll('.patrimonio_interactive_item[ativo-category][ativo-product]');
    const ativosElements = document.querySelectorAll('.ativos-grafico-item[ativo-category][ativo-product]');

    console.log(`📊 Elements found: ${patrimonioElements.length} patrimonio, ${ativosElements.length} ativos`);

    // Check for exact matches
    let successfulPairs = 0;
    const patrimonioAttribs = Array.from(patrimonioElements).map(el => ({
      category: el.getAttribute('ativo-category'),
      product: el.getAttribute('ativo-product')
    }));

    const ativosAttribs = Array.from(ativosElements).map(el => ({
      category: el.getAttribute('ativo-category'),
      product: el.getAttribute('ativo-product')
    }));

    patrimonioAttribs.forEach(p => {
      const hasMatch = ativosAttribs.some(a =>
        a.category === p.category && a.product === p.product
      );
      if (hasMatch) {
        successfulPairs++;
        console.log(`✅ PAIRED: "${p.category}" + "${p.product}"`);
      } else {
        console.warn(`❌ UNPAIRED: "${p.category}" + "${p.product}"`);
      }
    });

    const successRate = (successfulPairs / patrimonioElements.length) * 100;
    console.log(`\n📈 SUCCESS RATE: ${successfulPairs}/${patrimonioElements.length} (${successRate.toFixed(1)}%)`);

    if (successRate === 100) {
      console.log('🎉 ALL ELEMENTS CAN NOW BE PAIRED!');
    } else {
      console.warn('⚠️ Some elements still cannot be paired. Check logs above.');
    }

    return { successfulPairs, total: patrimonioElements.length, successRate };
  }

  // Restore original attributes (for debugging/testing)
  restoreOriginalAttributes() {
    console.log('🔄 Restoring original attributes...');

    this.originalAttributes.forEach((backup, key) => {
      const el = backup.element;
      if (el && el.parentNode) {
        el.setAttribute('ativo-category', backup.category);
        el.setAttribute('ativo-product', backup.product);
      }
    });

    this.fixesApplied = [];
    console.log('✅ Original attributes restored');
  }

  // Get detailed status
  getStatus() {
    return {
      initialized: this.isInitialized,
      fixesApplied: this.fixesApplied.length,
      hasBackup: this.originalAttributes.size > 0,
      fixes: this.fixesApplied
    };
  }

  // Cleanup method
  cleanup() {
    this.fixesApplied = [];
    this.originalAttributes.clear();
    this.isInitialized = false;
    console.log('🧹 AttributeFixerSystem cleaned up');
  }
}

// Export for use in main app
export default AttributeFixerSystem;
