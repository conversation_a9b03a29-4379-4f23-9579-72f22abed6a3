/* Step Navigation Styles - Mobile First */
.step-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e0e0e0;
  padding: 16px;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.step-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  position: relative;
  transition: all 0.2s ease;
}

.step-indicator:hover {
  background: rgba(0, 0, 0, 0.05);
}

.step-indicator:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  transition: all 0.2s ease;
}

.step-title {
  font-size: 11px;
  color: #666;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

.step-indicator.active .step-number {
  background: #000;
  color: white;
  transform: scale(1.1);
}

.step-indicator.active .step-title {
  color: #000;
  font-weight: 600;
}

.step-indicator.completed .step-number {
  background: #4caf50;
  color: white;
}

.step-indicator.completed .step-title {
  color: #4caf50;
}

/* Connection lines between steps */
.step-indicator:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 16px;
  right: -4px;
  width: 8px;
  height: 2px;
  background: #e0e0e0;
  z-index: -1;
}

.step-indicator.completed:not(:last-child)::after {
  background: #4caf50;
}

.step-buttons {
  display: flex;
  gap: 12px;
}

.step-btn {
  flex: 1;
  padding: 12px 24px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: white;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px; /* Touch target */
}

.step-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.step-btn:active:not(:disabled) {
  transform: translateY(0);
}

.step-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  box-shadow: none !important;
}

.step-btn:focus {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.prev-btn {
  background: #f8f9fa;
  color: #666;
  border-color: #ddd;
}

.prev-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #bbb;
  color: #333;
}

.next-btn {
  background: #000;
  color: white;
  border-color: #000;
}

.next-btn:hover:not(:disabled) {
  background: #333;
  border-color: #333;
}

.next-btn:disabled {
  background: #ccc;
  border-color: #ccc;
  color: #999;
}

/* Toast notifications */
.step-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 24px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  z-index: 1001;
  animation: slideInRight 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.step-toast--info {
  background: #4a90e2;
}

.step-toast--success {
  background: #4caf50;
}

.step-toast--error {
  background: #f44336;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Step sections styling */
.step-section {
  display: none; /* Hide all sections by default */
  min-height: 100vh; /* Full viewport height when active */
}

/* Show active section */
.step-section.active {
  display: block;
}

/* Animation variants when step navigation is active */
.step-section[data-step-method='simple'] {
  display: none;
}

.step-section[data-step-method='simple'].active {
  display: block;
}

.step-section[data-step-method='animated'] {
  display: block;
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.step-section[data-step-method='animated'].active {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Responsive Design */

/* Tablet */
@media (min-width: 768px) {
  .step-navigation {
    bottom: 20px;
    left: 20px;
    right: 20px;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
  }

  .step-progress {
    gap: 16px;
    margin-bottom: 20px;
  }

  .step-indicator {
    padding: 8px 12px;
  }

  .step-number {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .step-title {
    font-size: 12px;
  }

  .step-buttons {
    gap: 16px;
  }

  .step-btn {
    padding: 14px 32px;
    font-size: 16px;
    min-height: 52px;
  }

  .step-indicator:not(:last-child)::after {
    width: 16px;
    right: -8px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .step-navigation {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    right: auto;
    bottom: auto;
    width: auto;
    min-width: 600px;
    max-width: 800px;
    padding: 20px 32px;
  }

  .step-progress {
    gap: 24px;
    margin-bottom: 24px;
  }

  .step-indicator {
    padding: 12px 16px;
  }

  .step-number {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .step-title {
    font-size: 13px;
  }

  .step-buttons {
    gap: 20px;
    max-width: 400px;
    margin: 0 auto;
  }

  .step-btn {
    padding: 16px 40px;
    font-size: 16px;
    min-height: 56px;
  }

  .step-indicator:not(:last-child)::after {
    width: 24px;
    right: -12px;
  }

  /* Adjust step sections for top navigation */
  .step-section {
    min-height: calc(100vh - 160px);
  }

  .step-section.active {
    display: block;
  }
}

/* Large Desktop */
@media (min-width: 1440px) {
  .step-navigation {
    min-width: 700px;
    max-width: 900px;
  }

  .step-progress {
    gap: 32px;
  }

  .step-indicator {
    padding: 16px 20px;
  }

  .step-number {
    width: 44px;
    height: 44px;
    font-size: 20px;
  }

  .step-title {
    font-size: 14px;
  }

  .step-indicator:not(:last-child)::after {
    width: 32px;
    right: -16px;
  }
}

/* Print styles */
@media print {
  .step-navigation {
    display: none;
  }

  .step-section {
    display: block !important;
    opacity: 1 !important;
    transform: none !important;
    padding: 0;
    min-height: auto;
  }

  .step-section.active {
    display: block !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .step-navigation {
    border: 2px solid #000;
  }

  .step-number {
    border: 2px solid currentColor;
  }

  .step-btn {
    border-width: 3px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .step-navigation,
  .step-indicator,
  .step-number,
  .step-btn,
  .step-section {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }

  .step-toast {
    animation: none;
  }
}
