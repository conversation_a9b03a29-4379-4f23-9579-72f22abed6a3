# Asset Selection Filter System

Este sistema implementa a funcionalidade de seleção de ativos na Seção 2 e filtragem dinâmica na Seção 3.

## Funcionalidades

### ✅ Implementado

- **Checkboxes nos Ativos**: Cada ativo na Seção 2 agora possui um checkbox para seleção
- **Filtragem Dinâmica**: A Seção 3 mostra apenas os ativos selecionados na Seção 2
- **Contador de Ativos**: Atualização automática do contador de ativos selecionados
- **Botão Limpar**: Funcionalidade para limpar todas as seleções
- **Estados Visuais**: Feedback visual para ativos selecionados
- **Integração com Sistema Existente**: Compatível com todos os módulos existentes

### 🎯 Como Funciona

1. **Seção 2 - Seleção de Ativos**:
   - Cada ativo (dropdown e individual) recebe um checkbox
   - Clicar no checkbox ou no próprio ativo seleciona/deseleciona
   - Ativos selecionados ficam com destaque visual
   - Contador é atualizado em tempo real

2. **Seção 3 - Filtragem**:
   - Apenas ativos selecionados na Seção 2 são exibidos
   - Animação suave de entrada/saída
   - Sincronização baseada em `ativo-category` e `ativo-product`

## API Pública

### Métodos Disponíveis

```javascript
// Acessar o sistema
const assetFilter = window.ReinoCalculator.systems.assetSelectionFilter;

// Obter ativos selecionados
const selected = assetFilter.getSelectedAssets();

// Verificar se um ativo está selecionado
const isSelected = assetFilter.isAssetSelected('Renda Fixa', 'CDB');

// Selecionar um ativo programaticamente
assetFilter.selectAsset('Renda Fixa', 'CDB');

// Deselecionar um ativo
assetFilter.deselectAsset('Renda Fixa', 'CDB');

// Limpar todas as seleções
assetFilter.clearAllSelections();
```

### Eventos

O sistema emite o evento `assetFilterChanged` quando a seleção muda:

```javascript
document.addEventListener('assetFilterChanged', (event) => {
  const { selectedAssets, selectedCount } = event.detail;
  console.log(`${selectedCount} ativos selecionados:`, selectedAssets);
});
```

## Estrutura dos Arquivos

- **`src/modules/asset-selection-filter.js`**: Lógica principal do sistema
- **`src/styles/asset-selection-filter.css`**: Estilos para checkboxes e estados visuais

## Classes CSS Adicionadas

- `.asset-checkbox-container`: Container do checkbox
- `.asset-checkbox`: Estilo do checkbox personalizado
- `.selected-asset`: Estado visual de ativo selecionado
- `.asset-filtered-out`: Ativo oculto na Seção 3
- `.asset-filtered-in`: Ativo visível na Seção 3

## Integração

O sistema se integra automaticamente com:

- **Patrimony Sync**: Sincronização de valores
- **Simple Sync**: Barras visuais de progresso
- **Step Navigation**: Navegação entre seções
- **Currency System**: Formatação de valores

## Próximos Passos

Para implementar drag-and-drop no futuro:

1. O sistema já possui a estrutura de seleção
2. Pode ser facilmente estendido para incluir sortable/drag-drop
3. A API de seleção permanecerá compatível

## Debugging

Para debug, use o console do navegador:

```javascript
// Ver estado atual
ReinoCalculator.systems.assetSelectionFilter.getSelectedAssets();

// Ver todos os sistemas
ReinoCalculator.systems;
```
