# Reino Calculator - Estrutura Modular

## Visão Geral

O código JavaScript foi extraído do modelo Webflow e reorganizado em uma estrutura modular bem organizada. Todo o código funcional foi preservado, apenas reorganizado para melhor manutenibilidade.

## Estrutura dos Módulos

### 📁 `src/modules/`

#### 1. **currency-control.js**

- **Função**: Sistema de controle de moeda (botões +/-)
- **Responsabilidades**:
  - Controles de incremento/decremento inteligente
  - Cálculo de valores baseado em faixas
  - Integração com input principal
- **Funcionalidades Preservadas**: ✅ Todas mantidas

#### 2. **currency-formatting.js**

- **Função**: Sistema de formatação de moeda
- **Responsabilidades**:
  - Formatação de inputs em Real Brasileiro
  - Validação de entrada de dados
  - Integração com Currency.js
  - Eventos customizados para outros módulos
- **Funcionalidades Preservadas**: ✅ Todas mantidas

#### 3. **motion-animation.js**

- **Função**: Sistema de animações com Motion.js
- **Responsabilidades**:
  - Animações de botões e setas
  - Efeitos hover e press
  - Controle de visibilidade da seta interativa
  - Efeitos ripple
- **Funcionalidades Preservadas**: ✅ Todas mantidas

#### 4. **product-system.js**

- **Função**: Sistema de interação de produtos
- **Responsabilidades**:
  - Gerenciamento de estados ativos/inativos
  - Controle de sliders de alocação
  - Sistema de "pin" para manter itens ativos
  - Animações de transição entre estados
- **Funcionalidades Preservadas**: ✅ Todas mantidas

#### 5. **openai-allocation.js**

- **Função**: Integração com OpenAI para alocação
- **Responsabilidades**:
  - Processamento de linguagem natural
  - Comunicação com API OpenAI
  - Aplicação automática de alocações
  - Interface de configuração de API key
- **Funcionalidades Preservadas**: ✅ Todas mantidas

#### 6. **section-visibility.js**

- **Função**: Controle de visibilidade de seções
- **Responsabilidades**:
  - Intersection Observer para detecção
  - Animações premium de entrada/saída
  - Controle do componente flutuante
  - Gerenciamento de acessibilidade
- **Funcionalidades Preservadas**: ✅ Todas mantidas

#### 7. **patrimony-sync.js**

- **Função**: Sincronização de patrimônio
- **Responsabilidades**:
  - Sincronização entre input principal e alocações
  - Validação de over-allocation
  - Cache de valores no localStorage
  - Sistema de feedback visual
- **Funcionalidades Preservadas**: ✅ Todas mantidas

#### 8. **chart-animation.js**

- **Função**: Animações de gráficos com GSAP
- **Responsabilidades**:
  - Timeline de animações dos gráficos
  - Sincronização com range sliders
  - Controle de progresso
  - Correção de step problemático
- **Funcionalidades Preservadas**: ✅ Todas mantidas

### 📁 `src/app.js`

- **Função**: Módulo principal de integração
- **Responsabilidades**:
  - Inicialização coordenada de todos os sistemas
  - API global para debugging
  - Gerenciamento de erros
  - Ordem correta de inicialização

### 📁 `src/index.ts`

- **Função**: Ponto de entrada principal
- **Responsabilidades**:
  - Integração com Webflow
  - Importação do app.js
  - Preservação da funcionalidade original

## Dependências Externas

As seguintes bibliotecas externas são necessárias (já incluídas no modelo Webflow):

- **GSAP** - Animações de gráficos
- **Currency.js** - Cálculos de moeda precisos
- **Motion.js** - Animações modernas
- **Range Slider Element** - Controles de slider
- **jQuery** (opcional) - Para compatibilidade

## Inicialização

A inicialização acontece automaticamente na seguinte ordem:

1. `currencyFormatting` - Sistema base de moeda
2. `currencyControl` - Controles de incremento/decremento
3. `patrimonySync` - Sincronização central
4. `chartAnimation` - Animações de gráficos
5. `motionAnimation` - Efeitos de movimento
6. `productSystem` - Interações de produtos
7. `sectionVisibility` - Controle de visibilidade
8. `openaiAllocation` - IA (opcional)

## API Global de Debug

Após a inicialização, uma API global é disponibilizada:

```javascript
// Acesso aos sistemas
window.ReinoCalculator.systems.currencyControl
window.ReinoCalculator.systems.patrimonySync

// Controles úteis
window.ReinoCalculator.restart()
window.ReinoCalculator.getSystemStatus()

// Acesso organizados por categoria
window.ReinoCalculator.currency.control
window.ReinoCalculator.animation.chart
window.ReinoCalculator.data.patrimony
```

## Eventos Customizados

Os módulos comunicam-se através de eventos customizados:

- `reinoCalculatorReady` - App totalmente inicializado
- `currencyChange` - Mudança em valores de moeda
- `allocationChanged` - Mudança em alocação
- `sectionVisibilityChanged` - Mudança de visibilidade
- `patrimonySyncReady` - Sistema de patrimônio pronto

## Compatibilidade

- ✅ **100% Compatível** com o código Webflow original
- ✅ **Preserva** toda funcionalidade existente
- ✅ **Não quebra** integrações Webflow
- ✅ **Mantém** performance original
- ✅ **Adiciona** melhor organização e debug

## Benefícios da Modularização

1. **Manutenibilidade**: Cada módulo tem responsabilidade clara
2. **Testabilidade**: Módulos podem ser testados individualmente  
3. **Reutilização**: Módulos podem ser usados em outros projetos
4. **Debug**: API global facilita debugging
5. **Performance**: Inicialização otimizada e lazy loading
6. **Escalabilidade**: Fácil adicionar novos módulos

## Próximos Passos Recomendados

1. **Testes**: Implementar testes unitários para cada módulo
2. **TypeScript**: Migrar gradualmente para TypeScript
3. **Bundle**: Configurar bundling otimizado
4. **Documentação**: Adicionar JSDoc completo
5. **Performance**: Implementar lazy loading avançado
