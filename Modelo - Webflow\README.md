# ⚠️ WEBFLOW TEMPLATE FOLDER - READ ONLY ⚠️

## 🚨 CRITICAL WARNING: DO NOT EDIT THESE FILES

This folder contains **EXPORTED FILES FROM WEBFLOW** and is **STRICTLY READ-ONLY**.

### ❌ DO NOT

- Edit any HTML files
- Modify CSS files
- Change JavaScript files
- Add new files
- Delete existing files
- Move or rename files

### ✅ SAFE TO DO

- Read files for reference
- Copy code snippets for analysis
- Examine HTML structure
- Review CSS classes
- Understand component organization

## 🔄 Making Changes

**To modify these templates:**

1. **Go to Webflow platform** (the source of truth)
2. **Make your changes there**
3. **Export the updated files**
4. **Replace this entire folder** with the new export

## 📁 Folder Contents

- `index.html` - Main template page
- `untitled.html` - Additional template page
- `css/` - Webflow-generated stylesheets
- `fonts/` - Font assets from Webflow
- `images/` - Image assets from Webflow
- `js/` - Webflow-generated JavaScript

## 🛡️ Why This Policy Exists

- **Source of Truth:** Webflow platform is the authoritative source
- **Conflict Prevention:** Direct edits cause merge conflicts
- **Change Loss:** Manual edits are lost when Webflow exports are updated
- **Consistency:** Ensures repository matches Webflow platform

## 📚 More Information

See the main project policy: [WEBFLOW_POLICY.md](../WEBFLOW_POLICY.md)

---

**🔗 WEBFLOW PLATFORM IS THE SOURCE OF TRUTH - NOT THESE FILES**
