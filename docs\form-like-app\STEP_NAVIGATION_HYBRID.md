# Step Navigation System - Implementação Híbrida

## 📋 Visão Geral

Este documento detalha a implementação de um sistema de navegação por steps (wizard) usando **vanilla JavaScript** com abordagem híbrida adaptativa, mantendo toda a funcionalidade existente do Reino Calculator intocada.

## 🎯 Objetivos

- ✅ Converter layout atual em multi-step form
- ✅ Manter todos os módulos existentes funcionando
- ✅ Zero refactoring do código atual
- ✅ Performance otimizada para mobile e desktop
- ✅ Acessibilidade garantida
- ✅ Implementação em 2 dias

## 🏗️ Arquitetura do Sistema

### Estratégia Híbrida Adaptativa

```
DESKTOP (com animações):
├── Fade in/out transitions
├── Smooth translateY effects
├── Visual feedback enhanced
└── UX otimizada

MOBILE (performance first):
├── Display none/block direto
├── Sem animações desnecessárias
├── Bateria preservada
└── Performance máxima

ACESSIBILIDADE:
├── Detecta prefers-reduced-motion
├── Navegação por teclado
├── Screen reader friendly
└── Foco automático
```

## 📁 Estrutura de Arquivos

```
src/
├── modules/
│   └── step-navigation.js          # Sistema principal
├── styles/
│   └── step-navigation.css         # Estilos da navegação
└── app.js                          # Integração (1 linha)
```

## 🔧 Implementação Completa

### 1. Sistema Principal (step-navigation.js)

```javascript
/**
 * Step Navigation System - Híbrido Adaptativo
 * Implementa wizard flow mantendo funcionalidade existente
 */
export class StepNavigationSystem {
  constructor() {
    this.currentStep = 0;
    this.steps = [
      { id: '_0-home-section-calc-intro', name: 'intro', title: 'Introdução' },
      { id: '_1-section-calc-money', name: 'money', title: 'Renda e Patrimônio' },
      { id: '_2-section-calc-ativos', name: 'assets', title: 'Ativos' },
      { id: '_3-section-patrimonio-alocation', name: 'allocation', title: 'Alocação' }
    ];
    
    // Detecção de capacidades do dispositivo
    this.animationEnabled = window.matchMedia('(prefers-reduced-motion: no-preference)').matches;
    this.isMobile = window.innerWidth <= 768;
    this.isInitialized = false;
    
    // Sistema de validação
    this.validationRules = new Map();
    this.onStepChange = null;
    
    // Cache de elementos para performance
    this.sectionCache = new Map();
    this.navigationCache = null;
  }

  async init() {
    if (this.isInitialized) return;
    
    try {
      await this.waitForDOM();
      this.cacheSections();
      this.setupSections();
      this.createNavigation();
      this.setupValidation();
      this.setupEventListeners();
      this.showStep(0);
      
      this.isInitialized = true;
      console.log('✅ Step Navigation System initialized');
    } catch (error) {
      console.error('❌ Step Navigation initialization failed:', error);
    }
  }

  // Aguarda DOM estar completamente carregado
  waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve);
      } else {
        resolve();
      }
    });
  }

  // Cache de seções para performance
  cacheSections() {
    this.steps.forEach(step => {
      const section = document.querySelector(`.${step.id}`);
      if (section) {
        this.sectionCache.set(step.id, section);
      }
    });
  }

  // Setup adaptativo das seções
  setupSections() {
    this.steps.forEach((step, index) => {
      const section = this.sectionCache.get(step.id);
      if (section) {
        section.setAttribute('data-step', index);
        section.classList.add('step-section');
        
        // Estratégia adaptativa
        if (this.animationEnabled && !this.isMobile) {
          this.setupAnimatedSection(section, index);
        } else {
          this.setupSimpleSection(section, index);
        }
      }
    });
  }

  setupSimpleSection(section, index) {
    // Método Display None/Block para performance máxima
    section.style.display = index === 0 ? 'block' : 'none';
    section.setAttribute('data-step-method', 'simple');
  }

  setupAnimatedSection(section, index) {
    // Método com animações para desktop
    section.style.cssText = `
      transition: opacity 0.3s ease, transform 0.3s ease;
      opacity: ${index === 0 ? 1 : 0};
      transform: translateY(${index === 0 ? '0' : '20px'});
      pointer-events: ${index === 0 ? 'auto' : 'none'};
      position: relative;
      z-index: ${index === 0 ? 10 : 1};
    `;
    section.setAttribute('data-step-method', 'animated');
  }

  // Criação da interface de navegação
  createNavigation() {
    const navHtml = `
      <div class="step-navigation" role="navigation" aria-label="Navegação do formulário">
        <div class="step-progress" role="progressbar" aria-valuenow="1" aria-valuemin="1" aria-valuemax="${this.steps.length}">
          ${this.steps.map((step, i) => 
            `<div class="step-indicator ${i === 0 ? 'active' : ''}" 
                  data-step="${i}" 
                  role="button" 
                  tabindex="0"
                  aria-label="${step.title} - Passo ${i + 1} de ${this.steps.length}">
              <span class="step-number">${i + 1}</span>
              <span class="step-title">${step.title}</span>
            </div>`
          ).join('')}
        </div>
        <div class="step-buttons">
          <button class="step-btn prev-btn" disabled aria-label="Passo anterior">
            <span>← Anterior</span>
          </button>
          <button class="step-btn next-btn" aria-label="Próximo passo">
            <span>Próximo →</span>
          </button>
        </div>
      </div>
    `;

    // Adiciona no final do page-wrapper
    const pageWrapper = document.querySelector('.page-wrapper');
    if (pageWrapper) {
      pageWrapper.insertAdjacentHTML('beforeend', navHtml);
      this.navigationCache = document.querySelector('.step-navigation');
    }
  }

  // Setup de event listeners
  setupEventListeners() {
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');

    // Navegação por botões
    prevBtn?.addEventListener('click', () => this.previousStep());
    nextBtn?.addEventListener('click', () => this.nextStep());

    // Navegação por indicadores
    document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
      indicator.addEventListener('click', () => this.goToStep(index));
      indicator.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.goToStep(index);
        }
      });
    });

    // Navegação por teclado
    document.addEventListener('keydown', (e) => {
      if (e.altKey) { // Alt + seta para não conflitar com navegação normal
        if (e.key === 'ArrowLeft') {
          e.preventDefault();
          this.previousStep();
        }
        if (e.key === 'ArrowRight') {
          e.preventDefault();
          this.nextStep();
        }
      }
    });

    // Responsivo - redetecta capacidades em resize
    window.addEventListener('resize', this.debounce(() => {
      const wasMobile = this.isMobile;
      this.isMobile = window.innerWidth <= 768;
      
      if (wasMobile !== this.isMobile) {
        this.reinitializeSections();
      }
    }, 250));
  }

  // Sistema de validação
  setupValidation() {
    // Validação para step Introdução
    this.validationRules.set('intro', () => true); // Sempre válido

    // Validação para step Renda/Patrimônio
    this.validationRules.set('money', () => {
      const mainInput = document.querySelector('[is-main="true"]');
      if (!mainInput) return false;
      
      const value = this.parseInputValue(mainInput.value);
      return value > 0;
    });

    // Validação para step Ativos
    this.validationRules.set('assets', () => {
      const selectedProducts = document.querySelectorAll('.produto-item.is-selected');
      return selectedProducts.length > 0;
    });

    // Validação para step Alocação
    this.validationRules.set('allocation', () => {
      const totalAllocated = this.calculateTotalAllocation();
      return Math.abs(totalAllocated - 100) < 0.01; // Tolerância para floating point
    });

    // Validação em tempo real
    this.setupRealtimeValidation();
  }

  setupRealtimeValidation() {
    // Observa mudanças nos inputs relevantes
    const observer = new MutationObserver(() => {
      this.updateNavigationState();
    });

    // Elementos a observar
    const observeTargets = [
      '[is-main="true"]',
      '.produto-item',
      '.patrimonio_interactive_item'
    ];

    observeTargets.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        observer.observe(el, { 
          attributes: true, 
          childList: true, 
          subtree: true,
          attributeFilter: ['class', 'data-selected', 'value']
        });
        
        // Event listeners diretos para inputs
        if (el.tagName === 'INPUT') {
          el.addEventListener('input', () => this.updateNavigationState());
          el.addEventListener('change', () => this.updateNavigationState());
        }
      });
    });
  }

  // Navegação entre steps
  showStep(stepIndex) {
    if (stepIndex < 0 || stepIndex >= this.steps.length) return;

    const method = this.sectionCache.get(this.steps[stepIndex].id)?.getAttribute('data-step-method');
    
    if (method === 'simple') {
      this.showStepSimple(stepIndex);
    } else {
      this.showStepAnimated(stepIndex);
    }

    this.updateProgressIndicators(stepIndex);
    this.updateNavigationButtons(stepIndex);
    this.updateAccessibility(stepIndex);
    this.focusManagement(stepIndex);
    
    this.currentStep = stepIndex;
    
    // Callback para integração com outros sistemas
    if (this.onStepChange) {
      this.onStepChange(stepIndex, this.steps[stepIndex]);
    }

    // Scroll para o topo suavemente
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  showStepSimple(stepIndex) {
    this.steps.forEach((step, index) => {
      const section = this.sectionCache.get(step.id);
      if (section) {
        section.style.display = index === stepIndex ? 'block' : 'none';
      }
    });
  }

  showStepAnimated(stepIndex) {
    this.steps.forEach((step, index) => {
      const section = this.sectionCache.get(step.id);
      if (section) {
        if (index === stepIndex) {
          section.style.opacity = '1';
          section.style.transform = 'translateY(0)';
          section.style.pointerEvents = 'auto';
          section.style.zIndex = '10';
        } else {
          section.style.opacity = '0';
          section.style.transform = 'translateY(20px)';
          section.style.pointerEvents = 'none';
          section.style.zIndex = '1';
        }
      }
    });
  }

  nextStep() {
    if (this.currentStep >= this.steps.length - 1) {
      this.submitForm();
      return;
    }

    if (this.canProceedToNext()) {
      this.saveStepData(this.currentStep);
      this.showStep(this.currentStep + 1);
    } else {
      this.showValidationError();
    }
  }

  previousStep() {
    if (this.currentStep > 0) {
      this.showStep(this.currentStep - 1);
    }
  }

  goToStep(stepIndex) {
    // Só permite ir para steps anteriores ou atual
    if (stepIndex <= this.currentStep) {
      this.showStep(stepIndex);
    }
  }

  // Sistema de validação
  canProceedToNext() {
    const currentStepName = this.steps[this.currentStep]?.name;
    const validator = this.validationRules.get(currentStepName);
    return validator ? validator() : false;
  }

  // Atualização da interface
  updateProgressIndicators(stepIndex) {
    document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
      indicator.classList.toggle('active', index === stepIndex);
      indicator.classList.toggle('completed', index < stepIndex);
      
      // Acessibilidade
      indicator.setAttribute('aria-current', index === stepIndex ? 'step' : 'false');
    });

    // Atualiza progressbar
    const progressBar = document.querySelector('.step-progress');
    if (progressBar) {
      progressBar.setAttribute('aria-valuenow', stepIndex + 1);
    }
  }

  updateNavigationButtons(stepIndex) {
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');
    
    if (prevBtn) {
      prevBtn.disabled = stepIndex === 0;
    }
    
    if (nextBtn) {
      const isLastStep = stepIndex === this.steps.length - 1;
      const canProceed = this.canProceedToNext();
      
      nextBtn.disabled = !canProceed;
      nextBtn.querySelector('span').textContent = isLastStep ? 'Finalizar ✓' : 'Próximo →';
      nextBtn.setAttribute('aria-label', isLastStep ? 'Finalizar formulário' : 'Próximo passo');
    }
  }

  updateNavigationState() {
    this.updateNavigationButtons(this.currentStep);
  }

  updateAccessibility(stepIndex) {
    // Remove aria-hidden de todas as seções
    this.steps.forEach((step, index) => {
      const section = this.sectionCache.get(step.id);
      if (section) {
        section.setAttribute('aria-hidden', index !== stepIndex);
      }
    });
  }

  focusManagement(stepIndex) {
    // Foca no primeiro elemento focusável do step atual
    setTimeout(() => {
      const currentSection = this.sectionCache.get(this.steps[stepIndex].id);
      if (currentSection) {
        const firstFocusable = currentSection.querySelector(
          'input, button, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (firstFocusable) {
          firstFocusable.focus();
        }
      }
    }, 100);
  }

  // Validação e feedback
  showValidationError() {
    const currentStepName = this.steps[this.currentStep]?.name;
    let message = 'Por favor, complete os campos obrigatórios.';

    switch (currentStepName) {
      case 'money':
        message = 'Por favor, informe sua renda mensal.';
        break;
      case 'assets':
        message = 'Por favor, selecione pelo menos um produto de investimento.';
        break;
      case 'allocation':
        message = 'Por favor, complete a alocação do seu patrimônio (100%).';
        break;
    }

    // Toast notification ou alert simples
    this.showToast(message, 'error');
  }

  showToast(message, type = 'info') {
    // Remove toast anterior se existir
    const existingToast = document.querySelector('.step-toast');
    if (existingToast) {
      existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `step-toast step-toast--${type}`;
    toast.textContent = message;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'polite');

    document.body.appendChild(toast);

    // Remove após 4 segundos
    setTimeout(() => {
      toast.remove();
    }, 4000);
  }

  // Helpers e utilitários
  parseInputValue(value) {
    if (!value) return 0;
    // Remove formatação brasileira: "1.234.567,89" -> 1234567.89
    return parseFloat(value.replace(/\./g, '').replace(',', '.')) || 0;
  }

  calculateTotalAllocation() {
    // Integra com sistema existente de patrimônio
    const patrimonySyncSystem = window.ReinoCalculator?.data?.patrimony;
    if (patrimonySyncSystem && typeof patrimonySyncSystem.getTotalAllocationPercentage === 'function') {
      return patrimonySyncSystem.getTotalAllocationPercentage();
    }
    
    // Fallback: calcula manualmente
    let total = 0;
    const allocatedElements = document.querySelectorAll('[data-allocated-percentage]');
    allocatedElements.forEach(el => {
      total += parseFloat(el.getAttribute('data-allocated-percentage')) || 0;
    });
    return total;
  }

  saveStepData(stepIndex) {
    const stepData = this.collectStepData(stepIndex);
    const stepName = this.steps[stepIndex]?.name;
    
    // Salva no localStorage para recuperação
    try {
      localStorage.setItem(`reino_calc_step_${stepName}`, JSON.stringify({
        data: stepData,
        timestamp: new Date().toISOString()
      }));
    } catch (error) {
      console.warn('Não foi possível salvar dados do step:', error);
    }
  }

  collectStepData(stepIndex) {
    const section = this.sectionCache.get(this.steps[stepIndex].id);
    if (!section) return {};

    const data = {};
    
    // Coleta inputs
    const inputs = section.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
      if (input.name || input.id) {
        const key = input.name || input.id;
        data[key] = input.type === 'checkbox' ? input.checked : input.value;
      }
    });

    // Coleta elementos com estados especiais
    const selectedElements = section.querySelectorAll('.is-selected, [data-selected="true"]');
    data.selectedElements = Array.from(selectedElements).map(el => ({
      className: el.className,
      dataset: { ...el.dataset }
    }));

    return data;
  }

  // Submissão final
  async submitForm() {
    const allData = this.collectAllFormData();
    
    try {
      this.showToast('Enviando dados...', 'info');
      
      // Integração com backend (Supabase ou direto)
      if (window.supabase) {
        const { data, error } = await window.supabase
          .from('calculator_submissions')
          .insert({
            form_data: allData,
            submitted_at: new Date().toISOString(),
            user_agent: navigator.userAgent
          });
        
        if (error) throw error;
        
        this.showToast('Formulário enviado com sucesso!', 'success');
        this.onSubmissionSuccess(data);
      } else {
        // Fallback: log para desenvolvimento
        console.log('📊 Form submission data:', allData);
        this.showToast('Dados coletados com sucesso!', 'success');
        this.onSubmissionSuccess(allData);
      }
    } catch (error) {
      console.error('Erro no envio:', error);
      this.showToast('Erro ao enviar formulário. Tente novamente.', 'error');
    }
  }

  collectAllFormData() {
    const allData = {
      steps: {},
      metadata: {
        completed_at: new Date().toISOString(),
        total_time: this.getTotalTime(),
        device_info: {
          isMobile: this.isMobile,
          animationEnabled: this.animationEnabled,
          userAgent: navigator.userAgent.substr(0, 100)
        }
      }
    };

    // Coleta dados de todos os steps
    this.steps.forEach((step, index) => {
      allData.steps[step.name] = this.collectStepData(index);
    });

    return allData;
  }

  onSubmissionSuccess(data) {
    // Callback para ações pós-submissão
    // Redirecionar, mostrar página de obrigado, etc.
    console.log('✅ Form submitted successfully:', data);
    
    // Limpa dados salvos
    this.clearSavedData();
  }

  // Utilitários
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  getTotalTime() {
    // Implementar tracking de tempo se necessário
    return Date.now() - (this.startTime || Date.now());
  }

  reinitializeSections() {
    // Reinicializa seções quando dispositivo muda
    this.setupSections();
    this.showStep(this.currentStep);
  }

  clearSavedData() {
    this.steps.forEach(step => {
      try {
        localStorage.removeItem(`reino_calc_step_${step.name}`);
      } catch (error) {
        // Silent fail
      }
    });
  }

  // Cleanup
  cleanup() {
    if (this.navigationCache) {
      this.navigationCache.remove();
    }
    
    // Remove event listeners
    document.removeEventListener('keydown', this.keyboardHandler);
    window.removeEventListener('resize', this.resizeHandler);
    
    this.isInitialized = false;
    console.log('🧹 Step Navigation System cleaned up');
  }
}
```

### 2. Estilos CSS (step-navigation.css)

```css
/* Step Navigation Styles - Mobile First */
.step-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 15px;
  z-index: 1000;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.step-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 8px;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 8px;
  min-width: 60px;
  position: relative;
}

.step-indicator:hover {
  background: rgba(0, 0, 0, 0.05);
}

.step-indicator:focus {
  outline: 2px solid #4A90E2;
  outline-offset: 2px;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  margin-bottom: 4px;
}

.step-title {
  font-size: 11px;
  font-weight: 500;
  color: #666;
  text-align: center;
  transition: color 0.3s ease;
  line-height: 1.2;
}

.step-indicator.active .step-number {
  background: #000;
  color: white;
  transform: scale(1.1);
}

.step-indicator.active .step-title {
  color: #000;
  font-weight: 600;
}

.step-indicator.completed .step-number {
  background: #4CAF50;
  color: white;
}

.step-indicator.completed .step-title {
  color: #4CAF50;
}

/* Connection lines between steps */
.step-indicator:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 16px;
  left: calc(100% - 4px);
  width: 8px;
  height: 2px;
  background: #e0e0e0;
  transition: background 0.3s ease;
  z-index: -1;
}

.step-indicator.completed:not(:last-child)::after {
  background: #4CAF50;
}

.step-buttons {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.step-btn {
  flex: 1;
  padding: 12px 20px;
  border: 2px solid #ddd;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 48px; /* Touch target */
}

.step-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.step-btn:active:not(:disabled) {
  transform: translateY(0);
}

.step-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.step-btn:focus {
  outline: 2px solid #4A90E2;
  outline-offset: 2px;
}

.prev-btn {
  background: #f8f9fa;
  color: #666;
  border-color: #ddd;
}

.prev-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #bbb;
}

.next-btn {
  background: #000;
  color: white;
  border-color: #000;
}

.next-btn:hover:not(:disabled) {
  background: #333;
  border-color: #333;
}

.next-btn:disabled {
  background: #ccc;
  border-color: #ccc;
}

/* Toast notifications */
.step-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  z-index: 10001;
  animation: slideInRight 0.3s ease;
  max-width: 320px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.step-toast--info {
  background: #4A90E2;
}

.step-toast--success {
  background: #4CAF50;
}

.step-toast--error {
  background: #F44336;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Step sections styling */
.step-section {
  min-height: calc(100vh - 120px); /* Account for navigation */
  padding-bottom: 120px; /* Space for fixed navigation */
}

/* Responsive Design */

/* Tablet */
@media (min-width: 768px) {
  .step-navigation {
    padding: 20px 40px;
  }
  
  .step-progress {
    margin-bottom: 20px;
    gap: 12px;
  }
  
  .step-indicator {
    min-width: 80px;
    padding: 10px;
  }
  
  .step-number {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .step-title {
    font-size: 12px;
  }
  
  .step-indicator:not(:last-child)::after {
    width: 12px;
    left: calc(100% - 6px);
  }
  
  .step-btn {
    font-size: 16px;
    padding: 14px 24px;
  }
  
  .step-toast {
    top: 30px;
    right: 30px;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .step-navigation {
    padding: 25px 50px;
    position: relative;
    bottom: auto;
    background: white;
    border-top: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .step-progress {
    margin-bottom: 25px;
    gap: 20px;
  }
  
  .step-indicator {
    min-width: 120px;
    padding: 12px;
  }
  
  .step-number {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .step-title {
    font-size: 14px;
  }
  
  .step-indicator:not(:last-child)::after {
    width: 20px;
    left: calc(100% - 10px);
  }
  
  .step-buttons {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .step-btn {
    font-size: 16px;
    padding: 16px 32px;
    min-height: 56px;
  }
  
  .step-section {
    padding-bottom: 0; /* No fixed navigation on desktop */
    min-height: auto;
  }
}

/* Large Desktop */
@media (min-width: 1440px) {
  .step-navigation {
    padding: 30px 60px;
  }
  
  .step-progress {
    gap: 30px;
  }
  
  .step-indicator {
    min-width: 140px;
  }
  
  .step-number {
    width: 44px;
    height: 44px;
    font-size: 20px;
  }
  
  .step-title {
    font-size: 15px;
  }
}

/* Print styles */
@media print {
  .step-navigation {
    display: none;
  }
  
  .step-section {
    display: block !important;
    opacity: 1 !important;
    transform: none !important;
    pointer-events: auto !important;
    position: static !important;
    padding-bottom: 0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .step-navigation {
    border-top: 2px solid #000;
    background: #fff;
  }
  
  .step-indicator.active .step-number {
    background: #000;
    border: 2px solid #000;
  }
  
  .step-indicator.completed .step-number {
    background: #006600;
    border: 2px solid #006600;
  }
  
  .next-btn {
    background: #000;
    border: 2px solid #000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .step-navigation,
  .step-indicator,
  .step-number,
  .step-title,
  .step-btn,
  .step-toast,
  .step-section {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
  
  .step-btn:hover:not(:disabled) {
    transform: none;
  }
}
```

### 3. Integração no Projeto (app.js)

```javascript
// Em app-calc-reino/src/app.js - APENAS ADICIONE ESTAS LINHAS:

// 1. Import do sistema (linha ~15)
import { StepNavigationSystem } from './modules/step-navigation.js';

// 2. Adicione no constructor dos systems (linha ~30)
this.systems = {
  // ... sistemas existentes
  stepNavigation: new StepNavigationSystem(),
};

// 3. Adicione na ordem de inicialização (linha ~45)
this.initializationOrder = [
  'attributeFixer',
  'currencyFormatting', 
  'patrimonySync',
  'currencyControl',
  'simpleSync',
  'chartAnimation',
  'motionAnimation',
  'productSystem',
  'iaToggle',
  'scrollFloatAnimation',
  'sectionVisibility',
  'stepNavigation', // ADICIONE POR ÚLTIMO
  'openaiAllocation',
];

// 4. Adicione no setupGlobalAPI (linha ~180)
window.ReinoCalculator = {
  // ... APIs existentes
  
  navigation: {
    step: this.systems.stepNavigation,
    
    // Helpers para debug
    goToStep: (index) => this.systems.stepNavigation?.goToStep(index),
    nextStep: () => this.systems.stepNavigation?.nextStep(),
    prevStep: () => this.systems.stepNavigation?.previousStep(),
    getCurrentStep: () => this.systems.stepNavigation?.currentStep,
    canProceed: () => this.systems.stepNavigation?.canProceedToNext(),
  },
};
```

## 📋 Checklist de Implementação

### Fase 1: Setup Básico (2-3 horas)
- [ ] Criar arquivo `src/modules/step-navigation.js`
- [ ] Criar arquivo `src/styles/step-navigation.css`
- [ ] Adicionar imports no `app.js`
- [ ] Testar inicialização básica

### Fase 2: Navegação (3-4 horas)
- [ ] Implementar show/hide de seções
- [ ] Configurar botões de navegação
- [ ] Testar responsividade
- [ ] Implementar validação básica

### Fase 3: Integração (2-3 horas)
- [ ] Conectar com módulos existentes
- [ ] Testar validações específicas
- [ ] Configurar acessibilidade
- [ ] Testes em diferentes dispositivos

### Fase 4: Polish (1-2 horas)
- [ ] Animações finais
- [ ] Feedback visual
- [ ] Testes de usabilidade
- [ ] Documentação

## 🐛 Debugging e Testes

### Console Commands
```javascript
// No browser console:

// Status geral
ReinoCalculator.navigation.getCurrentStep()

// Navegar para step específico
ReinoCalculator.navigation.goToStep(2)

// Verificar se pode prosseguir
ReinoCalculator.navigation.canProceed()

// Forçar próximo step (bypass validação)
ReinoCalculator.navigation.step.showStep(3)

// Debug completo
ReinoCalculator.debugSync()
```

### Testes Manuais
1. **Navegação básica**: Clicar próximo/anterior
2. **Validação**: Tentar avançar sem completar campos
3. **Responsivo**: Testar em mobile/tablet/desktop
4. **Teclado**: Usar Alt+setas para navegar
5. **Acessibilidade**: Testar com screen reader
6. **Performance**: Verificar transições smooth

## 🔧 Customizações Opcionais

### Adicionar Progresso Percentual
```javascript
// No updateProgressIndicators()
const progressPercent = ((stepIndex + 1) / this.steps.length) * 100;
document.querySelector('.step-progress').style.setProperty('--progress', `${progressPercent}%`);
```

### Salvar Estado na URL
```javascript
// No showStep()
history.pushState(null, '', `#step-${stepIndex + 1}`);

// No init()
const urlStep = parseInt(location.hash.replace('#step-', '')) - 1;
if (urlStep >= 0 && urlStep < this.steps.length) {
  this.showStep(urlStep);
}
```

### Animações Customizadas
```javascript
// Substitua showStepAnimated() por:
showStepAnimated(stepIndex) {
  const direction = stepIndex > this.currentStep ? 1 : -1;
  
  this.steps.forEach((step, index) => {
    const section = this.sectionCache.get(step.id);
    if (section) {
      if (index === stepIndex) {
        section.style.transform = `translateX(${direction * 100}%)`;
        section.style.opacity = '0';
        section.style.pointerEvents = 'auto';
        
        setTimeout(() => {
          section.style.transform = 'translateX(0)';
          section.style.opacity = '1';
        }, 50);
      } else {
        section.style.transform = `translateX(${-direction * 100}%)`;
        section.style.opacity = '0';
        section.style.pointerEvents = 'none';
      }
    }
  });
}
```

## 📊 Métricas e Analytics

### Tracking de Conversão
```javascript
// Adicione no onStepChange callback
onStepChange: (stepIndex, stepData) => {
  // Google Analytics
  if (typeof gtag !== 'undefined') {
    gtag('event', 'step_progress', {
      step_name: stepData.name,
      step_number: stepIndex + 1,
      total_steps: this.steps.length
    });
  }
  
  // Custom analytics
  if (window.analytics) {
    window.analytics.track('Calculator Step Progress', {
      step: stepData.name,
      stepNumber: stepIndex + 1,
      totalSteps: this.steps.length,
      timestamp: new Date().toISOString()
    });
  }
}
```

### Coleta de Métricas de UX
```javascript
// No collectAllFormData()
metadata: {
  // ... dados existentes
  ux_metrics: {
    time_per_step: this.getTimePerStep(),
    validation_errors: this.getValidationErrorCount(),
    back_navigation_count: this.getBackNavigationCount(),
    completion_rate: this.getCompletionRate()
  }
}
```

## 🚀 Performance Tips

1. **Lazy Loading**: Carregue apenas o step atual
2. **Debouncing**: Use debounce em validações em tempo real
3. **Caching**: Cache elementos DOM para evitar re-queries
4. **Memory Management**: Cleanup event listeners ao destruir
5. **CSS Containment**: Use `contain: layout style` nas seções

## 📱 Considerações Mobile

1. **Touch Gestures**: Implemente swipe para navegar
2. **Virtual Keyboard**: Ajuste layout quando teclado aparece
3. **Orientation**: Suporte landscape/portrait
4. **Safe Areas**: Respeite safe areas em dispositivos com notch

---

## ⏱️ Timeline Realística

| Dia | Atividade | Tempo | Status |
|-----|-----------|-------|---------|
| **Dia 1** | Setup + Navegação Básica | 6-8h | Core functionality |
| **Dia 2** | Validação + Polish + Testes | 6-8h | Production ready |

**Total: 12-16 horas = 2 dias úteis**

Esta implementação te dará um **multi-step form profissional** mantendo toda funcionalidade existente intocada, com performance otimizada e acessibilidade completa.
