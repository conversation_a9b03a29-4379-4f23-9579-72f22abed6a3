/**
 * Step Navigation System - Híbrido Adaptativo
 * Implementa wizard flow mantendo funcionalidade existente
 */
export class StepNavigationSystem {
  constructor() {
    this.currentStep = 0;
    this.steps = [
      {
        id: '_0-home-section-calc-intro',
        name: 'intro',
        title: 'Introdu<PERSON>',
        validator: () => this.validateIntroStep(),
      },
      {
        id: '_1-section-calc-money',
        name: 'money',
        title: '<PERSON><PERSON>',
        validator: () => this.validateMoneyStep(),
      },
      {
        id: '_2-section-calc-ativos',
        name: 'assets',
        title: 'Ativos',
        validator: () => this.validateAssetsStep(),
      },
      {
        id: '_3-section-patrimonio-alocation',
        name: 'allocation',
        title: 'Alocação',
        validator: () => this.validateAllocationStep(),
      },
    ];

    this.sectionCache = new Map();
    this.navigationCache = null;
    this.isInitialized = false;
    this.startTime = Date.now();

    // Detecta capacidades do dispositivo
    this.supportsAnimations = !window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    this.isTouch = 'ontouchstart' in window;
    this.isMobile = window.innerWidth < 768;

    // Validadores específicos
    this.validators = {
      intro: () => this.validateIntroStep(),
      money: () => this.validateMoneyStep(),
      assets: () => this.validateAssetsStep(),
      allocation: () => this.validateAllocationStep(),
    };
  }

  async init() {
    if (this.isInitialized) return;

    try {
      await this.waitForDOM();
      await this.cacheSections();
      this.setupSections();
      this.createNavigation();
      this.setupEventListeners();
      this.setupValidation();

      // Mostra primeira seção
      this.showStep(0);

      this.isInitialized = true;

      // Escuta mudanças de orientação/resize
      window.addEventListener(
        'resize',
        this.debounce(() => {
          this.isMobile = window.innerWidth < 768;
          this.reinitializeSections();
        }, 250)
      );

      console.log('✅ Step Navigation System initialized');
    } catch (error) {
      console.error('❌ Step Navigation init failed:', error);
    }
  }

  // Aguarda DOM estar completamente carregado
  waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve, { once: true });
      }
    });
  }

  // Cache de seções para performance
  async cacheSections() {
    this.steps.forEach((step) => {
      const section = document.querySelector(`.${step.id}`);
      if (section) {
        this.sectionCache.set(step.id, section);
      } else {
        console.warn(`⚠️ Section not found: ${step.id}`);
      }
    });
  }

  // Setup adaptativo das seções
  setupSections() {
    this.steps.forEach((step, index) => {
      const section = this.sectionCache.get(step.id);
      if (!section) return;

      // Adiciona atributos necessários
      section.setAttribute('data-step', index);
      section.classList.add('step-section');

      // FORCE simple method for better compatibility
      // Sempre usar método simples para evitar problemas de espaçamento
      this.setupSimpleSection(section, index);
    });
  }

  setupSimpleSection(section, index) {
    // Método Display None/Block para performance máxima
    section.setAttribute('data-step-method', 'simple');
    section.style.display = index === 0 ? 'block' : 'none';
  }

  setupAnimatedSection(section, index) {
    // Método com animações para desktop
    section.setAttribute('data-step-method', 'animated');
    section.style.opacity = index === 0 ? '1' : '0';
    section.style.transform = index === 0 ? 'translateY(0)' : 'translateY(20px)';
    section.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
    section.style.display = 'block';
  }

  // Criação da interface de navegação
  createNavigation() {
    const navHtml = `
      <div class="step-navigation" role="navigation" aria-label="Navegação do formulário">
        <div class="step-progress" role="progressbar" aria-valuenow="1" aria-valuemin="1" aria-valuemax="${this.steps.length}">
          ${this.steps
            .map(
              (step, index) => `
            <button 
              class="step-indicator ${index === 0 ? 'active' : ''}" 
              data-step-index="${index}"
              aria-label="Ir para ${step.title}"
              tabindex="${index === 0 ? '0' : '-1'}"
            >
              <div class="step-number">${index + 1}</div>
              <div class="step-title">${step.title}</div>
            </button>
          `
            )
            .join('')}
        </div>
        
        <div class="step-buttons">
          <button class="step-btn prev-btn" disabled aria-label="Etapa anterior">
            ← Anterior
          </button>
          <button class="step-btn next-btn" aria-label="Próxima etapa">
            Próximo →
          </button>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', navHtml);
    this.navigationCache = document.querySelector('.step-navigation');
  }

  // Setup de event listeners
  setupEventListeners() {
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');

    prevBtn?.addEventListener('click', () => this.previousStep());
    nextBtn?.addEventListener('click', () => this.nextStep());

    // Indicadores de progresso
    document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
      indicator.addEventListener('click', () => this.goToStep(index));
    });

    // Navegação por teclado
    document.addEventListener('keydown', (e) => {
      if (e.altKey) {
        if (e.key === 'ArrowRight') {
          e.preventDefault();
          this.nextStep();
        } else if (e.key === 'ArrowLeft') {
          e.preventDefault();
          this.previousStep();
        }
      }
    });

    // Debounce para validação em tempo real
    this.debouncedValidation = this.debounce(() => {
      this.updateNavigationState();
    }, 250);
  }

  // Sistema de validação
  setupValidation() {
    // Validação para step Introdução
    this.setupRealtimeValidation();
  }

  setupRealtimeValidation() {
    // Observa mudanças nos inputs relevantes
    const inputSelector = 'input, select, textarea, range-slider';

    document.addEventListener('input', (e) => {
      if (e.target.matches(inputSelector)) {
        this.debouncedValidation();
      }
    });

    document.addEventListener('change', (e) => {
      if (e.target.matches(inputSelector)) {
        this.debouncedValidation();
      }
    });
  }

  // Navegação entre steps
  showStep(stepIndex) {
    if (stepIndex < 0 || stepIndex >= this.steps.length) return;

    const previousStep = this.currentStep;
    this.currentStep = stepIndex;

    // Salva dados do step anterior
    if (previousStep !== stepIndex) {
      this.saveStepData(previousStep);
    }

    // FORCE simple method sempre
    this.showStepSimple(stepIndex);

    this.updateProgressIndicators(stepIndex);
    this.updateNavigationButtons(stepIndex);
    this.updateAccessibility(stepIndex);
    this.focusManagement(stepIndex);

    // Scroll para o topo
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  showStepSimple(stepIndex) {
    this.steps.forEach((step, index) => {
      const section = this.sectionCache.get(step.id);
      if (section) {
        section.style.display = index === stepIndex ? 'block' : 'none';
      }
    });
  }

  showStepAnimated(stepIndex) {
    this.steps.forEach((step, index) => {
      const section = this.sectionCache.get(step.id);
      if (!section) return;

      if (index === stepIndex) {
        section.style.opacity = '1';
        section.style.transform = 'translateY(0)';
        section.style.pointerEvents = 'auto';
      } else {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.pointerEvents = 'none';
      }
    });
  }

  nextStep() {
    if (this.currentStep >= this.steps.length - 1) {
      this.submitForm();
      return;
    }

    if (this.canProceedToNext()) {
      this.showStep(this.currentStep + 1);
    } else {
      this.showValidationError();
    }
  }

  previousStep() {
    if (this.currentStep > 0) {
      this.showStep(this.currentStep - 1);
    }
  }

  goToStep(stepIndex) {
    // Só permite ir para steps anteriores ou atual
    if (stepIndex <= this.currentStep) {
      this.showStep(stepIndex);
    }
  }

  // Sistema de validação
  canProceedToNext() {
    const currentStepName = this.steps[this.currentStep]?.name;
    const validator = this.validators[currentStepName];
    return validator ? validator() : false;
  }

  validateIntroStep() {
    // Sempre permite avançar da introdução
    return true;
  }

  validateMoneyStep() {
    // Verifica se há valor no input principal
    const mainInput =
      document.querySelector('[is-main="true"]') ||
      document.querySelector('input[type="tel"]') ||
      document.querySelector('input[placeholder*="renda"], input[placeholder*="Renda"]');

    if (!mainInput) return false;

    const value = this.parseInputValue(mainInput.value);
    return value > 0;
  }

  validateAssetsStep() {
    // Verifica se há produtos selecionados
    const selectedProducts = document.querySelectorAll(
      '.produto-item.is-selected, .produto-item[data-selected="true"]'
    );
    return selectedProducts.length > 0;
  }

  validateAllocationStep() {
    // Verifica se a alocação total está correta
    const totalAllocated = this.calculateTotalAllocation();
    return Math.abs(totalAllocated - 100) < 0.01; // Permite pequena margem de erro
  }

  // Atualização da interface
  updateProgressIndicators(stepIndex) {
    document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
      indicator.classList.remove('active', 'completed');
      indicator.setAttribute('tabindex', '-1');

      if (index === stepIndex) {
        indicator.classList.add('active');
        indicator.setAttribute('tabindex', '0');
      } else if (index < stepIndex) {
        indicator.classList.add('completed');
      }
    });

    // Atualiza progressbar
    const progressbar = document.querySelector('.step-progress');
    if (progressbar) {
      progressbar.setAttribute('aria-valuenow', stepIndex + 1);
    }
  }

  updateNavigationButtons(stepIndex) {
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');

    if (prevBtn) {
      prevBtn.disabled = stepIndex === 0;
    }

    if (nextBtn) {
      const isLastStep = stepIndex === this.steps.length - 1;
      nextBtn.textContent = isLastStep ? 'Finalizar' : 'Próximo →';
      nextBtn.disabled = !this.canProceedToNext();
    }
  }

  updateNavigationState() {
    this.updateNavigationButtons(this.currentStep);
  }

  updateAccessibility(stepIndex) {
    // Remove aria-hidden de todas as seções
    this.steps.forEach((step, index) => {
      const section = this.sectionCache.get(step.id);
      if (section) {
        section.setAttribute('aria-hidden', index !== stepIndex);
      }
    });
  }

  focusManagement(stepIndex) {
    // Foca no primeiro elemento focusável do step atual
    setTimeout(() => {
      const currentSection = this.sectionCache.get(this.steps[stepIndex].id);
      if (currentSection) {
        const focusable = currentSection.querySelector(
          'input, button, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (focusable) {
          focusable.focus();
        }
      }
    }, 100);
  }

  // Validação e feedback
  showValidationError() {
    const currentStepName = this.steps[this.currentStep]?.name;
    const messages = {
      intro: 'Por favor, complete a introdução antes de continuar.',
      money: 'Por favor, informe sua renda mensal para continuar.',
      assets: 'Por favor, selecione pelo menos um ativo de interesse.',
      allocation: 'Por favor, complete a alocação do seu patrimônio (total deve ser 100%).',
    };

    const message = messages[currentStepName] || 'Por favor, complete os campos obrigatórios.';
    this.showToast(message, 'error');
  }

  showToast(message, type = 'info') {
    // Remove toast anterior se existir
    const existingToast = document.querySelector('.step-toast');
    if (existingToast) {
      existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `step-toast step-toast--${type}`;
    toast.textContent = message;
    toast.setAttribute('role', 'alert');

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 4000);
  }

  // Helpers e utilitários
  parseInputValue(value) {
    if (!value) return 0;
    return parseFloat(value.replace(/\./g, '').replace(',', '.')) || 0;
  }

  calculateTotalAllocation() {
    // Integra com sistema existente de patrimônio
    const allocationElements = document.querySelectorAll('[data-allocated-percentage]');
    let total = 0;

    allocationElements.forEach((element) => {
      const percentage = parseFloat(element.getAttribute('data-allocated-percentage')) || 0;
      total += percentage;
    });

    return total;
  }

  saveStepData(stepIndex) {
    const stepData = this.collectStepData(stepIndex);
    localStorage.setItem(`reino-step-${stepIndex}`, JSON.stringify(stepData));
  }

  collectStepData(stepIndex) {
    const section = this.sectionCache.get(this.steps[stepIndex].id);
    if (!section) return {};

    const data = {};
    const inputs = section.querySelectorAll('input, select, textarea');

    inputs.forEach((input) => {
      if (input.name || input.id) {
        const key = input.name || input.id;
        data[key] = input.value;
      }
    });

    return data;
  }

  // Submissão final
  async submitForm() {
    const allData = this.collectAllFormData();

    try {
      this.showToast('Processando informações...', 'info');

      // Aqui você pode integrar com APIs ou processar os dados
      await this.onSubmissionSuccess(allData);

      this.showToast('Calculadora finalizada com sucesso!', 'success');
    } catch (error) {
      this.showToast('Erro ao processar. Tente novamente.', 'error');
      console.error('Submission error:', error);
    }
  }

  collectAllFormData() {
    const allData = {
      timestamp: new Date().toISOString(),
      totalTime: this.getTotalTime(),
      steps: {},
    };

    this.steps.forEach((step, index) => {
      allData.steps[step.name] = this.collectStepData(index);
    });

    return allData;
  }

  onSubmissionSuccess(data) {
    // Callback para ações pós-submissão
    console.log('Form submitted successfully:', data);
    this.clearSavedData();
  }

  // Utilitários
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  getTotalTime() {
    // Implementar tracking de tempo se necessário
    return Date.now() - (this.startTime || Date.now());
  }

  reinitializeSections() {
    // Reinicializa seções quando dispositivo muda
    this.setupSections();
    this.showStep(this.currentStep);
  }

  clearSavedData() {
    this.steps.forEach((step, index) => {
      localStorage.removeItem(`reino-step-${index}`);
    });
  }

  // API Pública
  getCurrentStep() {
    return this.currentStep;
  }

  canProceed() {
    return this.canProceedToNext();
  }

  // Cleanup
  cleanup() {
    if (this.navigationCache) {
      this.navigationCache.remove();
    }

    this.steps.forEach((step) => {
      const section = this.sectionCache.get(step.id);
      if (section) {
        section.style.display = '';
        section.style.opacity = '';
        section.style.transform = '';
        section.removeAttribute('data-step');
        section.removeAttribute('data-step-method');
        section.classList.remove('step-section');
      }
    });

    this.isInitialized = false;
  }
}
